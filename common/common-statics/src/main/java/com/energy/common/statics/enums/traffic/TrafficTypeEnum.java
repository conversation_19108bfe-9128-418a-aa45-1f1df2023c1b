package com.energy.common.statics.enums.traffic;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车务类型
 */
public enum TrafficTypeEnum {

    ILLEGALLY_PREPARE("违章查缴", 1),
    ;

    /** 枚举值 */
    private int value;
    /** 描述 */
    private String desc;

    TrafficTypeEnum(String desc, int value) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static TrafficTypeEnum getEnum(int value) {
        TrafficTypeEnum resultEnum = null;
        for (TrafficTypeEnum areaTypeEnum : TrafficTypeEnum.values()) {
            if (areaTypeEnum.getValue().equals(value)) {
                resultEnum = areaTypeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<TrafficTypeEnum> toList(){
        return Arrays.asList(TrafficTypeEnum.values());
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> enumMap = new HashMap<>();
        for (TrafficTypeEnum areaTypeEnum : TrafficTypeEnum.values()) {
            enumMap.put(areaTypeEnum.value, areaTypeEnum.desc);
        }
        return enumMap;
    }
}
