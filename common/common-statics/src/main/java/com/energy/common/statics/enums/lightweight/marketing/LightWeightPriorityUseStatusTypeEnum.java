package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化领取权益使用状态
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightPriorityUseStatusTypeEnum {
    /**
     * 可使用
     */
    AVAILABLE(100,"可使用"),

    /**
     * 已使用
     */
    USED(101,"已使用"),

    /**
     * 已失效(权益时间过期)
     */
    EXPIRED(102,"已失效"),

    /**
     * 已撤销
     */
    ROLLBACK(103,"已撤销")
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightPriorityUseStatusTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightPriorityUseStatusTypeEnum getEnum(int value) {
        LightWeightPriorityUseStatusTypeEnum resultEnum = null;
        for (LightWeightPriorityUseStatusTypeEnum typeEnum : LightWeightPriorityUseStatusTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightPriorityUseStatusTypeEnum> toList() {
        return Arrays.asList(LightWeightPriorityUseStatusTypeEnum.values());
    }
}
