package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class NumberPwdValidator implements Validator<NumberPwd, String> {

	@Override
	public String validate(NumberPwd annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}

		return (ValidUtil.isNotEmpty(value) && ValidUtil.isNumberPwd(value)) ? null : annotation.message();
	}
}
