package com.energy.common.statics.enums.station;

import java.util.Arrays;
import java.util.List;

/**
 * @description 进账分流规则枚举
 * @author: zile
 * @date: 2020/11/18
 */
public enum FlowMchIncomeRuleEnum {

	/**
	 * 笔数分流
	 */
	NUM_SHUNT(1, "笔数分流"),

	/**
	 * 概率分流
	 */
	PROBABILITY_SHUNT(2, "概率分流"),
	;

	private int code;
	private String desc;

	FlowMchIncomeRuleEnum(int code, String text) {
		this.code = code;
		this.desc = text;
	}

	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	public static List<FlowMchIncomeRuleEnum> toList() {
		return Arrays.asList(FlowMchIncomeRuleEnum.values());
	}

	public static FlowMchIncomeRuleEnum getEnum(int value) {
		FlowMchIncomeRuleEnum resultEnum = null;
		FlowMchIncomeRuleEnum[] enumAry = FlowMchIncomeRuleEnum.values();
		for (int i = 0; i < enumAry.length; i++) {
			if (enumAry[i].getCode() == value) {
				resultEnum = enumAry[i];
				break;
			}
		}
		return resultEnum;
	}
}
