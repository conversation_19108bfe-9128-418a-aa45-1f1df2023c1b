package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化消费达标类型
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightConsumeThresholdTypeEnum {
    /**
     * 金额
     */
    AMOUNT(1, "金额"),
    /**
     * 升数
     */
    LITRES(2, "升数"),

    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightConsumeThresholdTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightConsumeThresholdTypeEnum getEnum(int value) {
        LightWeightConsumeThresholdTypeEnum resultEnum = null;
        for (LightWeightConsumeThresholdTypeEnum typeEnum : LightWeightConsumeThresholdTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightConsumeThresholdTypeEnum> toList() {
        return Arrays.asList(LightWeightConsumeThresholdTypeEnum.values());
    }
}
