package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

/**
 * 手机号码验证
 */
public class PhoneValidator implements Validator<Phone, String> {

	@Override
	public String validate(Phone annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}

		return (ValidUtil.isNotEmpty(value) && ValidUtil.isMobile(value)) ? null : annotation.message();
	}
}
