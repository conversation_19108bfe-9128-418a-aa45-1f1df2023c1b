package com.energy.common.statics.exceptions;

/**
 * 轻量化商户交易相关的异常
 * <AUTHOR>
 * @date 2022/9/2
 */
public class FlowTradeException extends BizException {
    public static final int ACCOUNT_PROCESS_REPEAT = *********;

    public FlowTradeException() {
    }

    public FlowTradeException(String msg) {
        super(BIZ_INVALID, msg);
    }

    public FlowTradeException(int sysErrCode, String msg) {
        super(sysErrCode, msg);
    }
}
