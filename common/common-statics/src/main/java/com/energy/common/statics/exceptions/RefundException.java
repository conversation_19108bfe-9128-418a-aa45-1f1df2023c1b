package com.energy.common.statics.exceptions;

import com.energy.common.statics.enums.common.BizCodeEnum;

import java.io.Serializable;

/**
 * 通道退款相关的异常 RefundException
 * 后续要改为继承 com.energy.common.statics.exceptions.BizException
 *
 * <AUTHOR>
 * @date 2020/02/18
 */
public class RefundException extends RuntimeException implements Serializable {
    private static final long serialVersionUID = -345568986985960990L;

    /**
     * 对外API的响应码 {@link ApiRespCodeEnum 的code}
     */
    protected String apiRespCode;
    /**
     * 对外API的业务异常码(由每个业务自行定义){@link BizCodeEnum 的code}
     */
    protected String apiErrCode;
    /**
     * 系统内部的错误码
     */
    protected int sysErrCode;
    /**
     * 错误描述
     */
    protected String errMsg;
    /**
     * 错误描述(兼容旧的异常)
     */
    protected String msg;

    public RefundException() {
        super();
    }

    public RefundException(BizCodeEnum bizCodeEnum) {
        super(bizCodeEnum.getMsg());
        this.apiErrCode = bizCodeEnum.getCode();
        this.errMsg = bizCodeEnum.getMsg();
        this.msg = bizCodeEnum.getMsg();
    }

    public RefundException(BizCodeEnum bizCodeEnum, String customMsg) {
        super(customMsg);
        this.apiErrCode = bizCodeEnum.getCode();
        this.errMsg = customMsg;
        this.msg = customMsg;
    }

    public String getApiRespCode() {
        return apiRespCode;
    }

    public void setApiRespCode(String apiRespCode) {
        this.apiRespCode = apiRespCode;
    }

    public String getApiErrCode() {
        return apiErrCode;
    }

    public void setApiErrCode(String apiErrCode) {
        this.apiErrCode = apiErrCode;
    }

    public int getSysErrCode() {
        return sysErrCode;
    }

    public void setSysErrCode(int sysErrCode) {
        this.sysErrCode = sysErrCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
