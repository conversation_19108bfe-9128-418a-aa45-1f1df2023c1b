package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

/**
 * 订单号校验
 */
public class OrderNoValidator implements Validator<OrderNo, String> {
	@Override
	public String validate(OrderNo annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		if (!ValidUtil.isOrderNo(value)) {
			return message;
		}
		int min = annotation.min();
		int max = annotation.max();
		int length = value.trim().length();
		if (min <= length && length <= max) {
			return null;
		} else {
			return message;
		}
	}
}
