package com.energy.common.statics.validation;

public class AllowIntValidator implements Validator<AllowInt, Integer> {

	@Override
	public String validate(AllowInt annotation, Integer value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		if (value == null) {
			return message;
		}
		int[] allows = annotation.allows();
		if (allows == null) {
			return message;
		}
		for (int allow : allows) {
			if (allow == value) {
				return null;
			}
		}
		return message;//没有匹配到值，则返回错误描述
	}
}
