package com.energy.common.statics.validation;

/**
 * 金额校验
 */
public class NotBlankValidator implements Validator<NotBlank, CharSequence> {

	@Override
	public String validate(NotBlank annotation, CharSequence value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		return (value != null && !value.toString().trim().isEmpty()) ? null : annotation.message();
	}
}
