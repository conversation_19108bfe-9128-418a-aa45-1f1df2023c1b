package com.energy.common.statics.constants.common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 机房名称的常量类
 */
public class IdcConst {
    /**
     * 南基域名的正则匹配表达式1，南基域名形如：10-10-90-10.pms-api.energy-web.svc.cluster.local
     */
    private final static String NANJI_HOST_REGEX_1 = "^(10-10-)(\\S)+(.svc.cluster.local)$";
    /**
     * 南基域名的正则匹配表达式2，南基域名形如：energy11n90.jopa.cc
     */
    private final static String NANJI_HOST_REGEX_2 = "^(energy)([1-9]|([1-9]\\d)|(1\\d\\d)|(2([0-4]\\d|5[0-5])))(n)([1-9]|([1-9]\\d)|(1\\d\\d)|(2([0-4]\\d|5[0-4])))(.jopa.cc)$";
    /**
     * 旗锐域名的正则匹配表达式1，旗锐域名形如：10-30-90-13.pms-api.energy-web.svc.cluster.local
     */
    private final static String QIRUI_HOST_REGEX_1 = "^(10-30-)(\\S)+(.svc.cluster.local)$";
    /**
     * 旗锐域名的正则匹配表达式2，旗锐域名形如：energy12q90.jopa.cc
     */
    private final static String QIRUI_HOST_REGEX_2 = "^(energy)([1-9]|([1-9]\\d)|(1\\d\\d)|(2([0-4]\\d|5[0-5])))(q)([1-9]|([1-9]\\d)|(1\\d\\d)|(2([0-4]\\d|5[0-4])))(.jopa.cc)$";

    public static final String NANJI_CODE = "nanji";//南基机房编码
    public static final String NANJI_NAME = "南基";//南基机房名称

    public static final String QIRUI_CODE = "qirui";//旗锐机房编码
    public static final String QIRUI_NAME = "旗锐";//南基机房名称

    public static final String ALL_CODE = "all";//南基和旗锐机房编码
    public static final String ALL_NAME = "南基,旗锐";//南基和旗锐机房名称

    /**
     * 匹配返回机房名称
     * @param hostName
     * @return
     */
    public static String getIdcCode(String hostName){
        // 南基-第一次域名匹配
        Pattern pattern = Pattern.compile(NANJI_HOST_REGEX_1);
        Matcher matcher = pattern.matcher(hostName);
        if(matcher.matches()){
            return NANJI_CODE;
        }
        // 南基-第二次域名匹配
        pattern = Pattern.compile(NANJI_HOST_REGEX_2);
        matcher = pattern.matcher(hostName);
        if(matcher.matches()){
            return NANJI_CODE;
        }

        // 旗锐-第一次域名匹配
        pattern = Pattern.compile(QIRUI_HOST_REGEX_1);
        matcher = pattern.matcher(hostName);
        if(matcher.matches()){
            return QIRUI_CODE;
        }
        // 旗锐-第二次域名匹配
        pattern = Pattern.compile(QIRUI_HOST_REGEX_2);
        matcher = pattern.matcher(hostName);
        if(matcher.matches()){
            return QIRUI_CODE;
        }

        throw new RuntimeException("未能正确识别的域名地址：" + hostName);
    }

    public static String getIdcName(String idcCode){
        if(NANJI_CODE.equals(idcCode)){
            return NANJI_NAME;
        }else if(QIRUI_CODE.equals(idcCode)){
            return QIRUI_NAME;
        }else if(ALL_CODE.equals(idcCode)){
            return ALL_NAME;
        }else{
            return "";
        }
    }
}
