package com.energy.common.statics.exceptions;

/**
 * @description 账务处理异常
 * @author: zhouf
 * @date: 2020/8/17
 */
public class AccountBizException extends BizException{

    /**------------------------- 新账务错误码，规范请参考新springBoot项目中BizException中描述的异常规范 --------------------------**/
    /**
     * 参数校验错误
     */
    public static final int PARAM_INVALID = *********;
    /**
     * 参数值校验错误
     */
    public static final int VALUE_INVALID = *********;
    /**
     * 业务校验错误
     */
    public static final int BIZ_INVALID = *********;


    /**
     * 数据库查询结果不唯一
     */
    public static final int DB_RESULT_NOT_ONE = *********;

    /**
     * 账户金额字段小于0
     */
    public static final int ACCOUNT_AMOUNT_FILED_LESS_THAN_ZERO = *********;

    /**
     * 账户记录不存在
     */
    public static final int ACCOUNT_RECORD_NOT_EXIT = *********;
    /**
     * 异步账务处理记录已存在
     */
    public static final int ACCOUNT_PROCESS_PENDING_UNIQUE_KEY_REPEAT = *********;
    /**
     * 重复账务处理
     */
    public static final int ACCOUNT_PROCESS_REPEAT = *********;

    /**
     * 账户状态处于"禁用"
     */
    public static final int ACCOUNT_STATUS_IS_INACTIVE = *********;

    /**
     * 余额小于不可用余额
     */
    public static final int BALANCE_LESS_THAN_UNBALANCE = *********;

    /**
     * 参数不一致
     */
    public static final int PARAM_NOT_MATCH = *********;

    /**
     * 账户可用余额不足
     */
    public static final int AVAILABLE_BALANCE_NOT_ENOUGH = *********;

    /**
     * 账户余额错误
     */
    public static final int BALANCE_ERROR = *********;

    /**
     * 原处理记录不存在
     */
    public static final int ORI_HANDLE_RECORD_NOT_EXIST = *********;

    /**
     * 已超过处理时限
     */
    public static final int OVER_HANDLE_TIME = *********;


    public AccountBizException() {
    }

    public AccountBizException(int code, String msg) {
        super(code, msg);
    }
}
