package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;


/**
 * 电子邮件校验
 *
 */
public class EmailValidator implements Validator<Email, String> {

    @Override
    public String validate(Email annotation, String value, String scene) {
        if (isSceneNotMatch(annotation.scenes(), scene)) {
            return null;
        }
        return (ValidUtil.isNotEmpty(value) && ValidUtil.isEmail(value)) ? null : annotation.message();
    }
}
