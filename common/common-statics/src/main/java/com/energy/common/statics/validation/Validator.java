package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

import java.lang.annotation.Annotation;

public interface Validator<A extends Annotation, T> {

    /**
     * 执行参数校验
     * @param annotation    当前注解
     * @param value         当前被校验参数的值
     * @param scene         当前校验场景
     * @return              若校验通过则返回null，若校验不通过则返回注解上的错误描述
     */
    String validate(A annotation, T value, String scene);

    default boolean isSceneNotMatch(String[] scenes, String scene) {
        //注解上没有配置场景、校验时没有指定场景的，都认为是需要校验的，也就是默认所有场景都需要校验
        if (scenes == null || scenes.length == 0 || ValidUtil.isEmpty(scene)) {
            return false;
        }
        for (String sceneTmp : scenes) {
            if (sceneTmp.equalsIgnoreCase(scene)) {
                return false;
            }
        }
        return true;
    }
}