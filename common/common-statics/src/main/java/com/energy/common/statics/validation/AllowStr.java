package com.energy.common.statics.validation;

import java.lang.annotation.*;
/**
 * 被注解参数须为 String 类型，校验规则为：不允许为null且去掉空格之后长度大于0、参数值须在给定的值里面
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AllowStrValidator.class)
public @interface AllowStr {
	String message() default "参数值不在可选值范围内";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};

    String[] allows() default {};
}