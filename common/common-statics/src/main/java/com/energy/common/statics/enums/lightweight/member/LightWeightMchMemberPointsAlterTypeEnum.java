package com.energy.common.statics.enums.lightweight.member;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @desc 积分变动类型
 * @author: xiebochang
 */
public enum LightWeightMchMemberPointsAlterTypeEnum {

    /**
     * 入账
     */
    CREDIT(1, "入账"),

    /**
     * 扣除
     */
    DEBIT(2, "扣除"),
    ;

    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightMchMemberPointsAlterTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List toList() {
        LightWeightMchMemberPointsAlterTypeEnum[] ary = LightWeightMchMemberPointsAlterTypeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据枚举值获取枚举属性.
     *
     * @param value
     *            枚举值.
     * @return enum 枚举属性.
     */
    public static LightWeightMchMemberPointsAlterTypeEnum getEnum(int value) {
        LightWeightMchMemberPointsAlterTypeEnum resultEnum = null;
        LightWeightMchMemberPointsAlterTypeEnum[] enumAry = LightWeightMchMemberPointsAlterTypeEnum.values();
        for (LightWeightMchMemberPointsAlterTypeEnum enumObj : enumAry) {
            if (enumObj.getValue() == value) {
                resultEnum = enumObj;
                break;
            }
        }
        return resultEnum;
    }

    /**
     * 将枚举类转换为map.
     *
     * @return Map<key, Map<attr, value>>
     */
    public static Map<String, Map<String, Object>> toMap() {
        LightWeightMchMemberPointsAlterTypeEnum[] ary = LightWeightMchMemberPointsAlterTypeEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<String, Map<String, Object>>();
        for (LightWeightMchMemberPointsAlterTypeEnum enumObj : ary) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(enumObj.getValue()));
            map.put("value", enumObj.getValue());
            map.put("desc", enumObj.getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }
}
