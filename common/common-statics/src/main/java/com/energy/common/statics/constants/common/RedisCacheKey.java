package com.energy.common.statics.constants.common;

public class RedisCacheKey {

    /**
     * pda已连接蓝牙打印机
     */
    public final static String CONNECTING_PDA_BLUETOOTH_PRINTER = "CONNECTING_PDA_BLUETOOTH_PRINTER";

    /**
     * api-app新redis key前缀
     */
    public static final String NEW_KEY_PREFIX = "APP_NKP_";

    /**
     * 车友村会员消费统计信息缓存hash key
     */
    public static final String FLOW_MEMBER_CONSUME_INFO_HASH_KEY = "FLOW_MEMBER_CON_INFO";

    /**
     * 车友村会员查询违章记录-cache key前缀
     */
    public static final String FLOW_MEMBER_ILLEGAL_RECORD_CACHE_KEY = "FLOW_MEMBER_ILLEGAL_RECORD_";

    /**
     * 班次统计执行标识缓存key
     */
    public static final String SHIFT_RECOUNT_PROCESS_CACHE_KEY = "SHIFT_RECOUNT_PROCESS_";

    /**
     * 流量统计缓存key前缀
     */
    public static final String FLOW_COUNT_CACHE_KEY_PREFIX = "FLOW_COUNT_CACHE_KEY_";

    /**
     * 商户订单时间管理缓存key前缀
     */
    public static final String MCH_ORDER_TIME_MANAGE_CACHE_KEY = "MCH_ORDER_TIME_MANAGE_CACHE_KEY";

    /**
     * 资金方预提交缓存key前缀
     */
    public static final String FUND_MCH_PRE_GRANT_CACHE_KEY = "FUND_MCH_PRE_GRANT_CACHE_KEY-";

    /**
     * 资金方订单号缓存key前缀
     */
    public static final String FUND_MCH_GRANT_ORDER_NO_CACHE_KEY = "FUND_MCH_GRANT_ORDER_NO_CACHE_KEY-";

    /**
     * 短信缓存key
     */
    public static final String SMS_CACHE_KEY = "SMS_CACHE_KEY:";

    /**
     * 轻量化商户-积分使用记录
     */
    public static final String LIGHT_WEIGHT_MEMBER_POINTS_USING_FLAG_KEY = "LIGHT_WEIGHT_MEMBER_POINTS_USING_FLAG_KEY:";

    /**
     * 油机厂商订单管理缓存key前缀
     */
    public static final String OIL_ENGINE_ORDER_INFO_KEY = "OIL_ENGINE_ORDER_INFO_KEY_";

    /**
     * 进账分流商户批次进账订单总数key前缀
     */
    public static final String INCOME_ORDER_TOTAL_NUM = "INCOME_ORDER_TOTAL_NUM_";
    /**
     * 进账分流主商户批次进账订单总数key前缀
     */
    public static final String INCOME_MAIN_ORDER_NUM = "INCOME_MAIN_ORDER_NUM_";
    /**
     * 进账分流次商户批次进账订单总数key前缀
     */
    public static final String INCOME_SECOND_ORDER_NUM = "INCOME_SECOND_ORDER_NUM_";

    /**
     * 轻量化商户-会员营销优惠劵使用记录
     */
    public static final String LIGHT_WEIGHT_MEMBER_COUPON_USING_FLAG_KEY = "LIGHT_WEIGHT_MEMBER_COUPON_USING_FLAG_KEY:";

    /**
     * 加油村api交班接口调用限制
     */
    public static final String API_MCH_SHIFT_KEY = "API_MCH_SHIFT_KEY:";

    /**
     * 积分快照发放情况统计
     */
    public static final String MEMBER_POINT_SNAPSHOT_DAILY_GRANT_STATISTIC_PREFIX = "MPS_GRANT_STATISTIC_";
    /**
     * 积分快照兑换情况统计
     */
    public static final String MEMBER_POINT_SNAPSHOT_DAILY_EXCHANGE_STATISTIC_PREFIX = "MPS_EXCHANGE_STATISTIC_";

    /**
     * 车险积分转让校验短信验证码标识
     */
    public static final String INSUR_POINT_GRANT_CHECK_SMS_CODE_CONTROLLER = "INSUR_POINT_GRANT_CHECK_SMS_CODE";

}
