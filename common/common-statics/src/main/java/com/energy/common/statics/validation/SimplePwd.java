package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不允许为null且去掉空格之后长度大于0、长度为6~12位的字母和数字组成
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = SimplePwdValidator.class)
public @interface SimplePwd {
    String message() default "密码格式错误";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};
}