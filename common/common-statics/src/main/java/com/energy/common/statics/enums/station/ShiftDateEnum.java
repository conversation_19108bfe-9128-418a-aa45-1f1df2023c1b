package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ShiftsDateEnum
 * 交班时间
 * <AUTHOR>
 * @date 2020/4/13
 */
public enum ShiftDateEnum {

    TODAY(0, "今天"),

    YESTERDAY(1, "昨天"),

    BEFORE_YESTERDAY(2, "前天"),
    ;

    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String msg;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    private ShiftDateEnum (int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static ShiftDateEnum getEnumByValue(int value) {
        ShiftDateEnum resultEnum = null;
        ShiftDateEnum[] enumAry = ShiftDateEnum.values();
        for (ShiftDateEnum orderStatusEnum : enumAry) {
            if (orderStatusEnum.value == value) {
                resultEnum = orderStatusEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List toList(){
        ShiftDateEnum[] ary = ShiftDateEnum.values();
        List list = new ArrayList();
        for(int i=0;i<ary.length;i++){
            Map<String,String> map = new HashMap<String,String>();
            map.put("value",String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getMsg());
            list.add(map);
        }
        return list;
    }

}
