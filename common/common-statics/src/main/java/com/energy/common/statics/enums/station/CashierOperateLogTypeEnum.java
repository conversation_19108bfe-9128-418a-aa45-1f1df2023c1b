package com.energy.common.statics.enums.station;

import java.util.Arrays;
import java.util.List;

/**
 * @desc: 收银员操作日志类型枚举
 * @author: fanfeihang
 * @date： 2020/2/26 14:24
 * @modified by:
 */
public enum CashierOperateLogTypeEnum {

    LOGIN("登录", 1),
    OFF_WORK("下班", 2),
    REFUND("退款", 3),
    MARK_ORDER("标记订单", 4),
    LOGIN_PWD_MODIFY("登录密码修改", 5),
    PAY_PWD_MODIFY("支付密码修改", 6),
    SHIFT_EXCHANGE("交班", 7),
    ;

    private String desc;

    private int code;

    CashierOperateLogTypeEnum (String desc, int code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static List<CashierOperateLogTypeEnum> toList(){
        return Arrays.asList(CashierOperateLogTypeEnum.values());
    }
}
