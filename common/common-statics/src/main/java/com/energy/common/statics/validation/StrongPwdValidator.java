package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class StrongPwdValidator implements Validator<StrongPwd, String> {

	@Override
	public String validate(StrongPwd annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}

		return (ValidUtil.isNotEmpty(value) && ValidUtil.isStrongPwd(value)) ? null : annotation.message();
	}
}
