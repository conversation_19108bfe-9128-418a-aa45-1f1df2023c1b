package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 BigDecimal 类型，校验规则为：不允许为null、参数值须介于 min ~ max 之间
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DecimalRangeValidator.class)
public @interface DecimalRange {
    String message() default "数值不在允许的区间范围内";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};

    /**
     * 最小值
     */
    double min() default 0.00;

    /**
     * 最大值
     */
    double max() default Double.MAX_VALUE;
}
