package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不能含有中文字符
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NotChineseValidator.class)
public @interface NotChinese {
	String message() default "不能含有中文字符";

	/**
	 * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
	 */
	String[] scenes() default {};
}
