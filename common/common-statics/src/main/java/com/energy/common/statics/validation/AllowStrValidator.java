package com.energy.common.statics.validation;

public class AllowStrValidator implements Validator<AllowStr, String> {
	@Override
	public String validate(AllowStr annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		String[] allows = annotation.allows();
		if (allows == null) {
			return message;
		}
		for (String allow : allows) {
			if (allow.equals(value)) {
				return null;
			}
		}
		return message;//没有匹配到值，则返回错误描述
	}
}
