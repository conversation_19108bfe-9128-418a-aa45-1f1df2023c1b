package com.energy.common.statics.enums.oilcard;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: xiebochang
 * @Title: 商户请求URL业务类型枚举
 * @date: 2020/4/14
 */
public enum UrlBizTypeEnum {

    CREATE_OIL_CARD("跳转至开卡页", 1),
    RECHARGE("跳转至充值页", 2),
    CONSUME("跳转至消费页", 3);

    /** 枚举值 */
    private int value;
    /** 描述 */
    private String desc;

    UrlBizTypeEnum (String desc, int value) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static UrlBizTypeEnum getEnum(int value) {
        UrlBizTypeEnum resultEnum = null;
        for (UrlBizTypeEnum typeEnum : UrlBizTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<UrlBizTypeEnum> toList(){
        return Arrays.asList(UrlBizTypeEnum.values());
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> enumMap = new HashMap<>();
        for (UrlBizTypeEnum typeEnum : UrlBizTypeEnum.values()) {
            enumMap.put(typeEnum.value, typeEnum.desc);
        }
        return enumMap;
    }
}
