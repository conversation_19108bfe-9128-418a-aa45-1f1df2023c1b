package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class SimplePwdValidator implements Validator<SimplePwd, String> {

	@Override
	public String validate(SimplePwd annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}

		return (ValidUtil.isNotEmpty(value) && ValidUtil.isSimplePwd(value)) ? null : annotation.message();
	}
}
