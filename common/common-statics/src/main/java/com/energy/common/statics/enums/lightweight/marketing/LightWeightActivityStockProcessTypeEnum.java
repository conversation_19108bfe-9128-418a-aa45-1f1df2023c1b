package com.energy.common.statics.enums.lightweight.marketing;

/**
 * 轻量化营销权活动份数处理
 * <AUTHOR>
 * @date 2022-10-11
 */
public enum LightWeightActivityStockProcessTypeEnum {
    /**
     * 添加
     */
    CREDIT(1, "添加"),

    /**
     * 扣减
     */
    DEBIT(2, "扣减"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightActivityStockProcessTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
