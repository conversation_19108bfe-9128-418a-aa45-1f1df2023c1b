package com.energy.common.statics.enums.message;

import com.energy.common.statics.annotations.DictIgnore;

/**
 * ReceiptBizTypeEnum 小票业务类型
 *
 * <AUTHOR>
 * @date 2020/3/11
 */
@DictIgnore
public enum ReceiptBizTypeEnum {
	PAY("pay", "收款"),
	REFUND("refund", "退款"),
    OFF_WORK("offWork", "下班"),
	RECHARGE("recharge", "充值"),
	CONSUME("consume", "消费"),
    OFF_WORK2("offWork2", "交班"),
	;
	/**
	 * 枚举值
	 */
	private String value;
	/**
	 * 描述
	 */
	private String msg;

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	ReceiptBizTypeEnum(String value, String desc) {
		this.value = value;
		this.msg = desc;
	}

	/**
	 * 根据枚举值获取枚举属性.
	 *
	 * @param value 枚举值.
	 * @return enum 枚举属性.
	 */
	public static ReceiptBizTypeEnum getEnum(String value) {
		ReceiptBizTypeEnum resultEnum = null;
		ReceiptBizTypeEnum[] enumAry = ReceiptBizTypeEnum.values();
		for (ReceiptBizTypeEnum typeEnum : enumAry) {
			if (typeEnum.getValue().equals(value)) {
				resultEnum = typeEnum;
				break;
			}
		}
		return resultEnum;
	}
}
