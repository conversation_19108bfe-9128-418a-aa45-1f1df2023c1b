package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ShiftCountTypeEnum
 * 交接班统计类型枚举
 *
 * <AUTHOR>
 * @date 2020/7/7
 */
public enum ShiftCountTypeEnum {

    ALL_SHIFT_COUNT(1, "全班次统计"),

    CASHIER_SHIFT_COUNT(2, "收银员班次统计"),
    ;
    /**
     * 枚举值
     */
    private Integer value;
    /**
     * 描述
     */
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    ShiftCountTypeEnum (Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ShiftCountTypeEnum getEnumByValue(Integer value) {
        ShiftCountTypeEnum resultEnum = null;
        ShiftCountTypeEnum[] enumAry = ShiftCountTypeEnum.values();
        for (ShiftCountTypeEnum orderStatusEnum : enumAry) {
            if (orderStatusEnum.value == value) {
                resultEnum = orderStatusEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List toList(){
        ShiftCountTypeEnum[] ary = ShiftCountTypeEnum.values();
        List list = new ArrayList();
        for(int i=0;i<ary.length;i++){
            Map<String,String> map = new HashMap<String,String>();
            map.put("value",String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

}
