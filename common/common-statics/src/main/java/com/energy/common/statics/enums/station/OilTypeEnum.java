package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 油品类型枚举
 *
 * <AUTHOR>
 * @date 2020/2/19
 */
public enum OilTypeEnum {

    /**
     * 0# 油
     */
    NO_0("0#", 1, OilVarietyEnum.DIESEL.getValue()),

    /**
     * 90# 油
     */
    NO_90("90#", 2, OilVarietyEnum.PETROL.getValue()),

    /**
     * 92# 油
     */
    NO_92("92#", 3, OilVarietyEnum.PETROL.getValue()),

    /**
     * 95# 油
     */
    NO_95("95#", 4, OilVarietyEnum.PETROL.getValue()),

    /**
     * 98# 油
     */
    NO_98("98#", 5, OilVarietyEnum.PETROL.getValue()),

    /**
     * 101# 油
     */
    NO_101("101#", 6, OilVarietyEnum.PETROL.getValue()),
    ;

    /**
     * 描述
     */
    private String desc;
    /**
     * 枚举值
     */
    private int value;

    private int oilVariety;

    /**
     * 构造函数
     */
    OilTypeEnum(String desc, int value, int oilVariety) {
        this.desc = desc;
        this.value = value;
        this.oilVariety = oilVariety;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public int getOilVariety() {
        return oilVariety;
    }

    public void setOilVariety(int oilVariety) {
        this.oilVariety = oilVariety;
    }

    public static OilTypeEnum getEnum(int value) {
        OilTypeEnum resultEnum = null;
        OilTypeEnum[] enumAry = OilTypeEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getValue() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public static Map<String, Map<String, Object>> toMap() {
        OilTypeEnum[] ary = OilTypeEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<>();
        for (int num = 0; num < ary.length; num++) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(ary[num].getValue()));
            map.put("value", String.valueOf(ary[num].getValue()));
            map.put("desc", ary[num].getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    public static List toList() {
        OilTypeEnum[] ary = OilTypeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

    public static OilTypeEnum getEnumByOilType(String oilType) {
        OilTypeEnum resultEnum = null;
        OilTypeEnum[] enumAry = OilTypeEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getDesc().equals(oilType)) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

}
