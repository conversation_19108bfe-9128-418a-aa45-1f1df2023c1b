package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不允许为null且去掉空格之后长度大于0、只允许数字和小数点、小数位最多只允许2位
 **/
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AmountValidator.class)
public @interface Amount {
	String message() default "金额格式错误";

	/**
	 * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
	 */
	String[] scenes() default {};
}
