package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class DateTimeValidator implements Validator<DateTime, String> {

	@Override
	public String validate(DateTime annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		return (ValidUtil.isNotEmpty(value) && ValidUtil.isDateTime(value)) ? null : annotation.message();
	}
}
