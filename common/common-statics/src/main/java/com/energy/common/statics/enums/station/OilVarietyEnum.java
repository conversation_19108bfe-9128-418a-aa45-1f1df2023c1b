package com.energy.common.statics.enums.station;

import java.util.Arrays;
import java.util.List;

/**
 * @desc 油品种类
 * @author: zhouf
 */
public enum OilVarietyEnum {
    /**
     * 汽油
     * #90、#92、#93、#95、#97、#98、#101
     */
    PETROL("汽油", 1),

    /**
     * 柴油
     * #0、#10、#-10、#-20、#-35、#-50
     */
    DIESEL("柴油", 2),

    /**
     * 非油品
     */
    NON_OIL("非油品", 3);


    private String desc;
    private int value;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    OilVarietyEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public static OilVarietyEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static List<OilVarietyEnum> toList() {
        return Arrays.asList(OilVarietyEnum.values());
    }
}
