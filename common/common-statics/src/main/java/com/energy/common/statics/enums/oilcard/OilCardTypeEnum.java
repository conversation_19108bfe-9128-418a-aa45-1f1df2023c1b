package com.energy.common.statics.enums.oilcard;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 油卡类型
 * @author: zhouf
 * @date: 2020/4/27
 */
public enum OilCardTypeEnum {

    /**
     * 个人版油卡
     */
    OWN("个人", 1),

    /**
     * 车队版油卡
     */
    CAR_TEAM("车队", 2),
    ;

    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String desc;

    OilCardTypeEnum(String desc, int value) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static OilCardTypeEnum getEnum(int value) {
        OilCardTypeEnum resultEnum = null;
        for (OilCardTypeEnum typeEnum : OilCardTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<OilCardTypeEnum> toList() {
        return Arrays.asList(OilCardTypeEnum.values());
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> enumMap = new HashMap<>();
        for (OilCardTypeEnum typeEnum : OilCardTypeEnum.values()) {
            enumMap.put(typeEnum.value, typeEnum.desc);
        }
        return enumMap;
    }
}