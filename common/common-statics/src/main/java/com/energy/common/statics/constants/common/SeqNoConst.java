package com.energy.common.statics.constants.common;

public class SeqNoConst {
    // 系统终端编号前缀
    public static final String PREFIX_SYS_TERMINAL_NO = "JYC";
    // 商户收款码编号前缀
    public static final String PREFIX_PAYMENT_CODE_NO = "JYCQR";
    // 商户收银员编号前缀
    public static final String PREFIX_MCH_CASHIER_NO = "MCN";
    // 合作渠道编号前缀
    public static final String PREFIX_COOP_CHANNEL_CODE = "CC";
    // 文件导出序列号
    public static final String PREFIX_FILE_EXPORT_NO = "1000";
    // 智能付--订单批次号前缀序号
    public static final String PAY_ORDER_BATCH_NO = "2002";
    // 智能付--详情流水号前缀
    public static final String PAY_ORDER_ITEM_TRX_NO = "2003";
    // 智能付--付款批次号前缀序号
    public static final String PAYMENT_BATCH_NO = "2004";
    // 智能付--付款详情流水号前缀
    public static final String PAYMENT_ITEM_TRX_NO = "2005";
    // 当日付--流水号前缀
    public static final String CURRENT_REMIT_TRX_NO = "2006";
    // 商户提现--流水号前缀
    public static final String WITHDRAW_ORDER_TRX_NO = "2007";
    // 服务费分账提现--流水号前缀
    public static final String FEE_ALT_ORDER_TRX_NO = "2008";
    // 加油村结算出款订单流水号前缀
    public static final String REMIT_ORDER_TRX_NO = "2009";
    // 加油村2.0代理商转账流水号前缀
    public static final String JIAYOUCUN2_AGENT_TRANSFER_TRX_NO = "2010";
    //钱包转账流水号
    public static final String WALLET_TRANSFER_TRX_NO = "2011";
    //钱包批量转账批次流水号
    public static final String WALLET_TRANSFER_BATCH_TRX_NO = "2012";


    // 支付订单流水号
    public static final String PAY_ORDER_TRX_NO_NEW = "4001";
    // 支付订单流水号
    public static final String PAY_ORDER_TRX_NO_SEQ = "4002";
    // 车务订单流水号
    public static final String TRAFFIC_ORDER_TRX_NO_SEQ = "4004";
    // 收单平台流水号
    @Deprecated
    public static final String PREFIX_PLAT_TRX_NO = "4003";
    // 轻量化支付订单流水号
    public static final String FLOW_PAY_ORDER_TRX_NO_SEQ = "4005";
    // 轻量化支付订单流水号
    public static final String FLOW_PAY_RECORD_TRX_NO_SEQ = "4006";
    // 消费订单流水号
    public static final String CONSUME_ORDER_TRX_NO_SEQ = "5001";
    // 采购订单流水号
    public static final String PURCHASE_ORDER_TRX_NO_SEQ = "5100";
    // 配额订单流水号
    public static final String QUOTA_ORDER_TRX_NO_SEQ = "5200";
    // 流量管家-账户流水号前缀
    public static final String FLOW_ACCOUNT_DETAIL_NO = "5050";
    // 流量管家-提现业务流水号前缀
    public static final String FLOW_WITHDRAW_NO = "5080";
    // 核销流水号前缀
    public static final String PRIORITY_USAGE_RECORD_TRX_NO_PREFIX = "6001";
    // 流量管家-核销流水号前缀
    public static final String FLOW_PRIORITY_USE_RECORD_TRX_NO = "6002";
    // 资金商户提现订单流水号
    public static final String FUND_MCH_GRANT_ORDER_PLAT_TRX_NO_PREFIX = "6003";
    // 资金商户充值订单流水号
    public static final String FUND_RECHARGE_ORDER_TRX_NO_PREFIX = "6004";
    // 会员积分明细流水号
    public static final String MEMBER_POINTS_DETAIL_TRX_NO = "MPD";
    // 会员积分明细流水号
    public static final String MEMBER_POINTS_CONSUME_TRX_NO = "MPC";
    // 会员积分领取订单号
    public static final String MEMBER_POINTS_RECEIVE_ORDER_NO = "MPR";
    // 会员积分调账流水号
    public static final String MEMBER_POINTS_ADJUSTMENT_TRX_NO = "MPAT";
    // 会员积分调账订单号
    public static final String MEMBER_POINTS_ADJUSTMENT_ORDER_NO = "MPAO";
    // 积分导入记录流水号前缀
    public static final String POINTS_IMPORT_RECORD_NO_PREFIX = "6005";
    // 积分兑换订单号前缀
    public static final String POINTS_CONSUME_ORDER_NO_PREFIX = "6005";
    // 证据链模板序列号前缀
    public static final String EVIDENCE_MODEL_SEQ_NO = "EVMO";
    // 车险会员推广订单序列号前缀
    public static final String PROMOTION_ORDER_SEQ_NO_PREFIX = "PO";
    // 车险会员推广订单序列号前缀
    public static final String FUND_POINT_SNAPSHOT_TRX_NO_SEQ = "MPSD";
    // 车险直接转让批次号
    public static final String FUND_GRANT_IMPORT_BATCH_NO_SEQ = "GIB";
    // 车险直接转让明细号
    public static final String FUND_GRANT_IMPORT_DETAIL_NO_SEQ = "GID";
    //车险兑换实物礼包编号
    public static final String FUND_GIFT_COUPON_PACKAGE_SEQ = "GCP";
    // 流量管家-会员id前缀
    public static final String FLOW_MEMBER_NO = "10";
    // 轻量化商户-会员id前缀
    public static final String LIGHT_WEIGHT_MCH_MEMBER_NO = "20";
    // 流量管家-账户id前缀
    public static final String FLOW_ACCOUNT_NO = "80";

    // 轻量化会员油卡调账订单号前缀
    public static final String FLOW_OIL_CARD_MEMBER_ACCOUNT_ADJUSTMENT_ORDER_NO = "OCAO";
    // 轻量化会员油卡账务处理流水号前缀
    public static final String FLOW_OIL_CARD_MEMBER_ACCOUNT__TRX_NO = "OCAT";

    /**
     * 轻量化会员营销活动ID前缀
     */
    public static final String LIGHTWEIGHT_MARKETING_ACTIVITY_ID = "MA";

    /**
     * 轻量化会员营销活动权益ID前缀
     */
    public static final String LIGHTWEIGHT_MARKRTING_ACTIVITY_PRIORITY_ID = "MAP";

    /**
     * 轻量化会员营销活动权益领取流水号
     */
    public static final String LIGHTWEIGHT_MARKRTING_ACTIVITY_PRIORITY_REVEIVE_NO = "MAPR";

    /**
     * 油卡卡号前缀
     */
    public static final String OIL_CARD_NO = "71";
    /**
     * 车队版油卡卡号前缀
     */
    public static final String CAR_TEAM_OIL_CARD_NO = "72";
    /**
     * 车队编号
     */
    public static final String CAR_TEAM_NO = "73";
    /**
     * 充值订单流水号
     **/
    public static final String RECHARGE_ORDER_TRX_NO_SEQ = "7001";
    //退款订单号
    public static final String PREFIX_REFUND_NO = "8001";
    //退款流水号
    public static final String PREFIX_REFUND_TRX_NO = "8002";
    //车务订单退款流水号
    public static final String PREFIX_TRAFFIC_REFUND_TRX_NO = "8003";
    //退款流水号
    public static final String PREFIX_REFUND_TRX_NO_NEW = "8004";

    // 账户编号
    public static final String ACCOUNT_NO = "8008";
    // 账户流水号
    public static final String ACCOUNT_PROCESS_NO_PREFIX = "6000";
    // 班次记录流水号
    public static final String SHIFT_RECORD_NO_SEQ = "8416";
    /**
     * 分账方操作流水前缀
     */
    public static final String ALT_MCH_TRX_NO = "3300";

    /**
     * 渠道商户编号
     */
    public static final String CHANNEL_MCH_NO = "CM";

    /**
     * 邮件流水号
     */
    public static final String EMAIL_TRX_NO = "EMAIL";

    /**
     * 公众号充值流水号前缀
     */
    public static final String GZH_RECHARGE_TRX_NO = "7002";

    /**
     * 轻量化会员加油储值卡 充值订单前缀
     */
    public static final String FLOW_OIL_CARD_RECHARGE_ORDER_NO_SEQ = "7003";
    /**
     * 轻量化会员加油储值卡 充值订单前缀
     */
    public static final String FLOW_OIL_CARD_RECHARGE_TRX_NO_SEQ = "7004";
    /**
     * 轻量化会员加油储值卡 消费订单前缀
     */
    public static final String FLOW_OIL_CARD_CONSUME_ORDER_NO_SEQ = "7005";

    /**
     * 税筹提现导入批次前缀
     */
    public static final String TAX_GRANT_IMPORT_RECORD_NO_PREFIX = "6006";
    /**
     * 税筹提现平台流水号前缀
     */
    public static final String TAX_GRANT_GRANT_RECORD_NO_PREFIX = "6007";

    /**
     * 税筹充值流水号前缀
     */
    public static final String TAX_RECHARGE_ORDER_TRX_NO = "6008";

    /**
     * 车险退款订单流水号前缀
     */
    public static final String FUND_REFUND_ORDER_TRX_NO_SEQ = "8005";

    /**
     * 电子协议前缀
     */
    public static final String AGREEMENT_NO_PREFIX = "AN";
    /**
     * e签宝个人平台账户前缀
     */
    public static final String ESIGN_PERSON_ACCOUNT_NO_PREFIX = "EPAN";

    /**
     * 车险直接转让提现导入批次前缀
     */
    public static final String CAR_GRANT_IMPORT_RECORD_NO_PREFIX = "6009";
    /**
     * 发票流水号前缀
     */
    public static final String INVOICE_NO_PREFIX = "FP";
    /**
     * 保险礼品包导入批次号前缀
     */
    public static final String INSURANCE_GIFT_COUPON_BATCH_NO = "LPB";

    /**
     * 保险增值服务导入批次前缀
     */
    public static final String INSURANCE_VALUE_ADD_SERVICE_BATCH_NO = "VASB";
    /**
     * 保险增值服务导入明细前缀
     */
    public static final String INSURANCE_VALUE_ADD_SERVICE_DETAIL_NO = "VASD";

    /**
     * 保险清单结算导入批次前缀
     */
    public static final String INSURANCE_SETTLE_DATA_BATCH_NO = "ISDB";

    /**
     * 保险清单结算导入卡劵明细前缀
     */
    public static final String INSURANCE_SETTLE_DATA_DETAIL_NO = "ISDD";


    //4位长度的最大值
    public static final long MAX_VALUE_FOUR_LENGTH = 9999;
    //6位长度的最大值
    public static final long MAX_VALUE_SIX_LENGTH = 999999;
    //7位长度的最大值
    public static final long MAX_VALUE_SEVEN_LENGTH = 9999999;
    //8位长度的最大值
    public static final long MAX_VALUE_EIGHT_LENGTH = 99999999;
}

