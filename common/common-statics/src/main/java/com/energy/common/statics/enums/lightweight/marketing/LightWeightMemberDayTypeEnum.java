package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化会员日类型
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightMemberDayTypeEnum {
    /**
     * 天会员日
     */
    DAY(1, "天会员日"),

    /**
     * 周会员日
     */
    WEEK(2, "周会员日"),
    /**
     * 月会员日
     */
    MONTH(3, "月会员日"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightMemberDayTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightMemberDayTypeEnum getEnum(int value) {
        LightWeightMemberDayTypeEnum resultEnum = null;
        for (LightWeightMemberDayTypeEnum typeEnum : LightWeightMemberDayTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightMemberDayTypeEnum> toList() {
        return Arrays.asList(LightWeightMemberDayTypeEnum.values());
    }
}
