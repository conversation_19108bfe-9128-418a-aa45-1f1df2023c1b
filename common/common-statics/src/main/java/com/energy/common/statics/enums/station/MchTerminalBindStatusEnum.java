package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @描述:商户终端绑定状态
 *
 *
 */
public enum MchTerminalBindStatusEnum {

	BIND("绑定", 100),
	UNBIND("解绑", 101),
	;

	/** 描述 */
	private String desc;
	/** 枚举值 */
	private int value;
	/** 构造函数 */
	MchTerminalBindStatusEnum (String desc, int value) {
		this.desc = desc;
		this.value = value;
	}
	
	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static MchTerminalBindStatusEnum getEnum(int value){
		MchTerminalBindStatusEnum resultEnum = null;
		MchTerminalBindStatusEnum[] enumAry = MchTerminalBindStatusEnum.values();
		for(int i = 0;i<enumAry.length;i++){
			if(enumAry[i].getValue() == value){
				resultEnum = enumAry[i];
				break;
			}
		}
		return resultEnum;
	}
	
	public static Map<String, Map<String, Object>> toMap() {
		MchTerminalBindStatusEnum[] ary = MchTerminalBindStatusEnum.values();
		Map<String, Map<String, Object>> enumMap = new HashMap<>();
		for (int num = 0; num < ary.length; num++) {
			Map<String, Object> map = new HashMap<>();
			String key = String.valueOf(getEnum(ary[num].getValue()));
			map.put("value", String.valueOf(ary[num].getValue()));
			map.put("desc", ary[num].getDesc());
			enumMap.put(key, map);
		}
		return enumMap;
	}
	
	
	@SuppressWarnings({"unchecked", "rawtypes" })
	public static List toList(){
		MchTerminalBindStatusEnum[] ary = MchTerminalBindStatusEnum.values();
		List list = new ArrayList();
		for(int i=0;i<ary.length;i++){
			Map<String,String> map = new HashMap<String,String>();
			map.put("value",String.valueOf(ary[i].getValue()));
			map.put("desc", ary[i].getDesc());
			list.add(map);
		}
		return list;
	}

}
