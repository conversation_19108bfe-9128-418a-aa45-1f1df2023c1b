package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @描述: 终端类型枚举
 *
 */
public enum TerminalTypeEnum {

	A_PDA("pda", 1),
	C_CASH_REGISTER("收银机", 2),
	B_BLUETOOTH_PRINTER("蓝牙打印机", 3),
	B_NET_PRINTER("网络打印机", 4),
	S_SCNAER_GUN("扫码枪", 5),
	S_SMLALL_FLASH("小闪", 6),
	P_POS("pos机", 7),
	FLOW_PDA("流量平台pda", 8),
	PLUS_PDA("加油村PLUS-pda", 9),
	PLUS_CASHIER("加油村PLUS-双面屏", 10),
	;

	/**
	 * 描述
	 */
	private String desc;
	/**
	 * 枚举值
	 */
	private int value;

	/**
	 * 构造函数
	 */
	TerminalTypeEnum(String desc, int value) {
		this.desc = desc;
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static TerminalTypeEnum getEnum(int value){
		TerminalTypeEnum resultEnum = null;
		TerminalTypeEnum[] enumAry = TerminalTypeEnum.values();
		for(int i = 0;i<enumAry.length;i++){
			if(enumAry[i].getValue() == value){
				resultEnum = enumAry[i];
				break;
			}
		}
		return resultEnum;
	}

	public static Map<String, Map<String, Object>> toMap() {
		TerminalTypeEnum[] ary = TerminalTypeEnum.values();
		Map<String, Map<String, Object>> enumMap = new HashMap<>();
		for (int num = 0; num < ary.length; num++) {
			Map<String, Object> map = new HashMap<>();
			String key = String.valueOf(getEnum(ary[num].getValue()));
			map.put("value", String.valueOf(ary[num].getValue()));
			map.put("desc", ary[num].getDesc());
			enumMap.put(key, map);
		}
		return enumMap;
	}


	@SuppressWarnings({"unchecked", "rawtypes" })
	public static List toList(){
		TerminalTypeEnum[] ary = TerminalTypeEnum.values();
		List list = new ArrayList();
		for(int i=0;i<ary.length;i++){
			Map<String,String> map = new HashMap<String,String>();
			map.put("value",String.valueOf(ary[i].getValue()));
			map.put("desc", ary[i].getDesc());
			list.add(map);
		}
		return list;
	}

}
