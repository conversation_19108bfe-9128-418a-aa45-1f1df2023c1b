package com.energy.common.statics.exceptions;

import com.energy.common.statics.enums.common.ApiRespCodeEnum;

public class RemitBizException extends BizException {

    public RemitBizException(String message) {
        super(message);
    }

    public RemitBizException(int sysErrCode, String message) {
        super(sysErrCode, message);
    }

    public RemitBizException(String apiErrCode, String message) {
        super(ApiRespCodeEnum.ACCEPT_FAIL.getCode(), apiErrCode, message);
    }

    public RemitBizException(String message, Throwable cause) {
        super(message, cause);
    }

    public RemitBizException(int sysErrCode, String message, Throwable cause) {
        super(sysErrCode, message, cause);
    }
}
