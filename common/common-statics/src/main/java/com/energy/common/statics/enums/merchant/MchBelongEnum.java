package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户归属枚举值
 */
public enum MchBelongEnum {
    JAYOCUN_V1(1, "加油村1.0"),
    JAYOCUN_V2(2, "加油村2.0"),

    ;
    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String desc;

    private MchBelongEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static MchBelongEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
