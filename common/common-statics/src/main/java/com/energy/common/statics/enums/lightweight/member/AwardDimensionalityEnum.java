package com.energy.common.statics.enums.lightweight.member;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 轻量化商户会员-获取积分奖励的维度
 *
 * <AUTHOR>
 */
public enum AwardDimensionalityEnum {

	OIL_LITERS("按1升1个积分", 0),
	OIL_AMOUNT("按1元1个积分", 1),
	;

	AwardDimensionalityEnum(String desc, int value){
		this.desc = desc;
		this.value = value;
	}
	
	/** 描述 */
	private String desc;
	/** 枚举值 */
	private int value;
	
	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	/**
	 * 根据枚举值获取枚举属性.
	 * 
	 * @param value
	 *            枚举值.
	 * @return enum 枚举属性.
	 */
	public static AwardDimensionalityEnum getEnum(int value) {
		AwardDimensionalityEnum resultEnum = null;
		AwardDimensionalityEnum[] enumAry = AwardDimensionalityEnum.values();
        for (AwardDimensionalityEnum enumObj : enumAry) {
            if (enumObj.getValue() == value) {
                resultEnum = enumObj;
                break;
            }
        }
		return resultEnum;
	}

	/**
	 * 将枚举类转换为map.
	 * 
	 * @return Map<key, Map<attr, value>>
	 */
	public static Map<String, Map<String, Object>> toMap() {
		AwardDimensionalityEnum[] ary = AwardDimensionalityEnum.values();
		Map<String, Map<String, Object>> enumMap = new HashMap<String, Map<String, Object>>();
        for (AwardDimensionalityEnum enumObj : ary) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(enumObj.getValue()));
            map.put("value", enumObj.getValue());
            map.put("desc", enumObj.getDesc());
            enumMap.put(key, map);
        }
		return enumMap;
	}

	/**
	 * 将枚举类转换为list.
	 * 
	 * @return List<Map<String, Object>> list.
	 */
	public static List<Map<String, Object>> toList() {
		AwardDimensionalityEnum[] ary = AwardDimensionalityEnum.values();
		List<Map<String, Object>> list = new ArrayList<>();
        for (AwardDimensionalityEnum enumObj : ary) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", enumObj.getValue());
            map.put("desc", enumObj.getDesc());
            list.add(map);
        }
		return list;
	}
}
