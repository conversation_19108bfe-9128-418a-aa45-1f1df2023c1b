package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

/**
 * 金额校验
 */
public class NotEmptyValidator implements Validator<NotEmpty, Object> {

	@Override
	public String validate(NotEmpty annotation, Object value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		return ValidUtil.isNotEmpty(value) ? null : annotation.message();
	}
}
