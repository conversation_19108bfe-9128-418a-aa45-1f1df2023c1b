package com.energy.common.statics.enums.traffic;

/**
 * @desc 补充资料Map
 * @author: zhouf
 */
public enum ReplenishImgEnum {

    /**
     *
     */
    VEHICLE_ORG_FRONT("j", "行驶证正本正面照片"),
    VEHICLE_ORG_BACK("k", "行驶证正本背面照片"),
    VEHICLE_COPY_FRONT("l", "行驶证副本正面照片"),
    VEHICLE_COPY_BACK("m", "行驶证副本背面照片"),
    DRIVING_ORG_FRONT("n", "驾驶证正本正面照片"),
    DRIVING_ORG_BACK("o", "驾驶证正本背面照片"),
    DRIVING_COPY_FRONT("p", "驾驶证副本正面照片"),
    DRIVING_COPY_BACK("q", "驾驶证副本背面照片"),
    DECISION("r", "决定书照片"),
    INSURANCE("s", "交强险图片"),
    TOKEN("t", "上游上传办理凭证图片"),
    HALF_PHOTO("jj", "车主本人上半身自拍照"),

    //TODO
    ;

    private String code;

    private String desc;

    ReplenishImgEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReplenishImgEnum getEnum(String code) {
        ReplenishImgEnum resultEnum = null;
        ReplenishImgEnum[] enumAry = ReplenishImgEnum.values();
        for (ReplenishImgEnum methodEnum : enumAry) {
            if (methodEnum.getCode().equals(code)) {
                resultEnum = methodEnum;
                break;
            }
        }
        return resultEnum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
