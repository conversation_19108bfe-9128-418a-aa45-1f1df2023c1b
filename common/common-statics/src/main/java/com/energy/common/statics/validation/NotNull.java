package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数可为任意类型，校验规则为：不能为null
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NotNullValidator.class)
public @interface NotNull {
    String message() default "参数不能为null";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};
}
