package com.energy.common.statics.enums.merchant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @描述: 商户类型 . <br/>
 * @作者: WuShuicheng .
 * @创建时间: 2013-9-12,上午11:16:23 .
 * @版本: 1.0 .
 */
public enum MerchantTypeEnum {
	INDIVIDUAL("个体工商户", 11),
	ENTERPRISE("企业", 12),
	MCH_TEST("测试商户", 13),
	PERSONAL("个人", 10),

	;
	
	/** 描述 */
	private String desc;
	/** 枚举值 */
	private int value;

	private MerchantTypeEnum(String desc, int value) {
		this.desc = desc;
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	public static MerchantTypeEnum getEnum(int value){
		MerchantTypeEnum resultEnum = null;
		MerchantTypeEnum[] enumAry = MerchantTypeEnum.values();
		for(int i = 0;i<enumAry.length;i++){
			if(enumAry[i].getValue() == value){
				resultEnum = enumAry[i];
				break;
			}
		}
		return resultEnum;
	}
	
	public static Map<String, Map<String, Object>> toMap() {
		MerchantTypeEnum[] ary = MerchantTypeEnum.values();
		Map<String, Map<String, Object>> enumMap = new HashMap<String, Map<String, Object>>();
		for (int num = 0; num < ary.length; num++) {
			Map<String, Object> map = new HashMap<String, Object>();
			String key = String.valueOf(getEnum(ary[num].getValue()));
			map.put("value", String.valueOf(ary[num].getValue()));
			map.put("desc", ary[num].getDesc());
			enumMap.put(key, map);
		}
		return enumMap;
	}
	
	public static String getName(int value){
		String result = null;
		MerchantTypeEnum[] enumAry = MerchantTypeEnum.values();
		for(int i = 0;i<enumAry.length;i++){
			if(value == enumAry[i].getValue()){
				result = enumAry[i].name();
				break;
			}
		}
		return result;
	}
	
	public static String getDesc(int value){
		String result = null;
		MerchantTypeEnum[] enumAry = MerchantTypeEnum.values();
		for(int i = 0;i<enumAry.length;i++){
			if(value == enumAry[i].getValue()){
				result = enumAry[i].getDesc();
				break;
			}
		}
		return result;
	}
	
	public static List toList(){
		MerchantTypeEnum[] ary = MerchantTypeEnum.values();
		List list = new ArrayList();
		for(int i=0;i<ary.length;i++){
			Map<String,String> map = new HashMap<String,String>();
			map.put("value",String.valueOf(ary[i].getValue()));
			map.put("desc", ary[i].getDesc());
			list.add(map);
		}
		return list;
	}
	
	public static Map<String,Map<String,String>> toDicMap(){
		MerchantTypeEnum[] ary = MerchantTypeEnum.values();
		Map<String, Map<String, String>> enumMap = new HashMap<>();
		Map<String, String> map = new HashMap<>();
		for (int num = 0; num < ary.length; num++) {
			map.put(String.valueOf(ary[num].getValue()), ary[num].getDesc());
		}
		enumMap.put("merchantType", map);
		return enumMap;
	}

}
