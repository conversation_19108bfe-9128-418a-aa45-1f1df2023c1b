package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 CharSequence/Collection/Map/数组 类型，校验规则为：不允许为null、如果是CharSequence子类则去掉空格之后长度大于0、如果是 Collection/Map/数组 则size须大于0
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NotEmptyValidator.class)
public @interface NotEmpty {
    String message() default "参数为null或为空";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};
}
