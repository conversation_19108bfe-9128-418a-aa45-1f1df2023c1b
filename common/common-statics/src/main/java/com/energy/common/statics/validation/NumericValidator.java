package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class NumericValidator implements Validator<Numeric, String> {

	@Override
	public String validate(Numeric annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}

		return (ValidUtil.isNotEmpty(value) && ValidUtil.isNumeric(value)) ? null : annotation.message();
	}
}
