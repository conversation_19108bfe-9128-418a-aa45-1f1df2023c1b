package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 CharSequence及其子类(String/StringBuffer/StringBuilder等)，校验规则为：不能为null、长度须介于 min ~ max 之间
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = LengthValidator.class)
public @interface Length {
    String message() default "参数长度不在允许的区间范围内";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};

    int min() default 0;

    int max() default Integer.MAX_VALUE;
}
