package com.energy.common.statics.enums.oilcard;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 油卡调整状态枚举类
 */
public enum TransferStatusEnum {
	/**
	 * 处理成功
	 */
	SUCCESS("调账成功", 100),
	/**
	 * 处理失败
	 */
	ABNORMAL("调账失败", 101),
	/**
	 * 处理中
	 */
	PROCESSING("处理中", 102),
	;

	/**
	 * 描述
	 */
	private String desc;
	/**
	 * 数值
	 */
	private int value;

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	TransferStatusEnum(String desc, int value) {
		this.desc = desc;
		this.value = value;
	}

	public static TransferStatusEnum getEnumByValue(int value) {
		TransferStatusEnum resultEnum = null;
		TransferStatusEnum[] enumAry = TransferStatusEnum.values();
		for (TransferStatusEnum orderStatusEnum : enumAry) {
			if (orderStatusEnum.value == value) {
				resultEnum = orderStatusEnum;
				break;
			}
		}
		return resultEnum;
	}

	@SuppressWarnings({"rawtypes", "unchecked"})
	public static List toList() {
		TransferStatusEnum[] ary = TransferStatusEnum.values();
		List list = new ArrayList();
		for (int i = 0; i < ary.length; i++) {
			Map<String, String> map = new HashMap<String, String>();
			map.put("value", String.valueOf(ary[i].getValue()));
			map.put("desc", ary[i].getDesc());
			list.add(map);
		}
		return list;
	}

}
