package com.energy.common.statics.enums.lightweight.member;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @desc 积分变动业务类型
 * @author: xiebochang
 */
public enum LightWeightMchMemberPointsBizTypeEnum {

    /**
     * 加油积分领取
     */
    AWARD_BY_PAY(1, "加油积分获取"),

    /**
     * 兑换加油优惠
     */
    DEDUCT_ORDER_AMOUNT(2, "兑换加油优惠"),
    /**
     * 调账
     */
    ACCOUNT_REGULATION(3, "调账"),
    /**
     * 扣减积分重新发放
     */
    RE_GRANT(4, "扣减积分-重新发放"),
    /**
     * 赠送积分-回退
     */
    ROLLBACK(5, "赠送积分-回退"),
    /**
     * 积分兑换使用
     */
    CONSUME(6, "积分兑换"),
    ;

    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightMchMemberPointsBizTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List toList() {
        LightWeightMchMemberPointsBizTypeEnum[] ary = LightWeightMchMemberPointsBizTypeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据枚举值获取枚举属性.
     *
     * @param value
     *            枚举值.
     * @return enum 枚举属性.
     */
    public static LightWeightMchMemberPointsBizTypeEnum getEnum(int value) {
        LightWeightMchMemberPointsBizTypeEnum resultEnum = null;
        LightWeightMchMemberPointsBizTypeEnum[] enumAry = LightWeightMchMemberPointsBizTypeEnum.values();
        for (LightWeightMchMemberPointsBizTypeEnum enumObj : enumAry) {
            if (enumObj.getValue() == value) {
                resultEnum = enumObj;
                break;
            }
        }
        return resultEnum;
    }

    /**
     * 将枚举类转换为map.
     *
     * @return Map<key, Map<attr, value>>
     */
    public static Map<String, Map<String, Object>> toMap() {
        LightWeightMchMemberPointsBizTypeEnum[] ary = LightWeightMchMemberPointsBizTypeEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<String, Map<String, Object>>();
        for (LightWeightMchMemberPointsBizTypeEnum enumObj : ary) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(enumObj.getValue()));
            map.put("value", enumObj.getValue());
            map.put("desc", enumObj.getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }
}
