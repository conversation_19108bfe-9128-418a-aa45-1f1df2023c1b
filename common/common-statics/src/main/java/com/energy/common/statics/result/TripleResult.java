package com.energy.common.statics.result;

import java.io.Serializable;

/**
 * 用在一个方法需要返回3个值的情况
 * @param <L>
 * @param <M>
 * @param <R>
 */
public class TripleResult<L,M,R> implements Serializable {
    private L left;
    private M middle;
    private R right;

    public TripleResult(){}

    public TripleResult(L left, M middle, R right){
        this.left = left;
        this.middle = middle;
        this.right = right;
    }

    public L getLeft() {
        return left;
    }

    public void setLeft(L left) {
        this.left = left;
    }

    public M getMiddle() {
        return middle;
    }

    public void setMiddle(M middle) {
        this.middle = middle;
    }

    public R getRight() {
        return right;
    }

    public void setRight(R right) {
        this.right = right;
    }
}
