package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 CharSequence及其子类(String/StringBuffer/StringBuilder等)，校验规则为：不能为null、去掉空格之后长度大于0
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NotBlankValidator.class)
public @interface NotBlank {
    String message() default "不能为null或空字符串";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};
}
