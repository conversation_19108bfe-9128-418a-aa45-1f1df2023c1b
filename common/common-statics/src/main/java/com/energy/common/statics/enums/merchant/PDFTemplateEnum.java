package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * PDF 模板枚举
 */
public enum PDFTemplateEnum {
    INVOICE("INVOICE-APPLY-TEMPLATE.ftl", "电子发票申请书模板"),
    API_AGREEMENT("API-AGREEMENT.ftl", "支付服务合作协议"),
    PROMOTION_SYSTEM_AGREEMENT("PROMOTION-SYSTEM-AGREEMENT.ftl", "数字服务合作协议"),
    API_SUPPLEMENT_AGREEMENT("API-SUPPLEMENT-AGREEMENT.ftl", "支付服务合作协议(补充)"),
    PROMOTION_SYSTEM_SUPPLE_AGREEMENT("PROMOTION-SYSTEM-SUPPLE-AGREEMENT.ftl", "数字服务合作协议(补充)"),
    ;

    /**
     * 枚举值
     */
    private String name;
    /**
     * 模板名称
     */
    private String desc;

    private PDFTemplateEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static PDFTemplateEnum getEnum(String kk) {
        return Arrays.stream(values()).filter(p -> p.name().equals(kk)).findFirst().orElse(null);
    }

    public static void main(String[] args) {
        PDFTemplateEnum templateEnum = getEnum("API_AGREEMENT");
        System.out.println(templateEnum);
    }
}
