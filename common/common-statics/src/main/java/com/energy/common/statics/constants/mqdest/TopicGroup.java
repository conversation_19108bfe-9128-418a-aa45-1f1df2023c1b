package com.energy.common.statics.constants.mqdest;

import com.energy.common.statics.annotations.Queue;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 队列分组（消费分组），属于逻辑上的业务分组，跟ActiveMQ本身无关，如果是RocketMQ，可作为消息的tags
 */
public class TopicGroup {
    @Queue(desc = "定时任务")
    public static final String TIMER_GROUP = "timer";
    @Queue(desc = "邮件")
    public static final String EMAIL_GROUP = "email";
    @Queue(desc = "支付相关")
    public static final String PAYMENT_GROUP = "payment";
    @Queue(desc = "账务相关")
    public static final String ACCOUNT_GROUP = "account";
    @Queue(desc = "出款相关")
    public static final String REMIT_GROUP = "remit";
    @Queue(desc = "运维相关")
    public static final String DEV_OPS = "devops";
    @Queue(desc = "交班相关")
    public static final String SHIFT_GROUP = "shift";
    @Queue(desc = "退款相关")
    public static final String REFUND_GROUP = "refund";
    @Queue(desc = "通用")//不好归类，和其他流程都设计到的，例如：商户通知
    public static final String COMMON_GROUP = "common";
    @Queue(desc = "消费相关")
    public static final String CONSUME_GROUP = "consume";
    @Queue(desc = "油卡相关")
    public static final String OIL_CARD_GROUP = "oilCard";
    @Queue(desc = "会员相关")
    public static final String MEMBER_GROUP = "member";
    @Queue(desc = "产品相关")
    public static final String PRODUCT = "product";
    @Queue(desc = "分账相关")
    public static final String ALT = "alt";
    @Queue(desc = "商户-收银员-管理员相关")
    public static final String MERCHANT = "merchant";
    @Queue(desc = "易加油退款相关")
    public static final String EJIAYOU_REFUND_GROUP = "ejiayou_refund";
    @Queue(desc = "轻量化商户退款相关")
    public static final String LIGHT_MCH_REFUND_GROUP = "light_mch_refund";
    @Queue(desc = "易加油油站查询相关")
    public static final String EJIAYOU_GROUP = "eJiaYou";
    @Queue(desc = "车友村营销相关")
    public static final String FLOW_MARKETING_GROUP = "flow_marketing";
    @Queue(desc =  "车友村车务相关")
    public static final String  TRAFFIC_GROUP = "flow_traffic";
    @Queue(desc = "壳牌相关")
    public static final String QIAOPAI = "qiaopai";
    @Queue(desc = "智享会相关")
    public static final String ZHI_XIANG_HUI = "zhixianghui";
    @Queue(desc = "车友村积分相关")
    public static final String CHEYOCUN_POINTS = "cheyocun_points";
    @Queue(desc = "税筹相关")
    public static final String TAX_GROUP = "tax";

    /**
     * key为属性值，value为该属性上@Queue注解的描述内容
     *
     * @return
     */
    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        Field[] fields = TopicGroup.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null) {
                continue;
            }

            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(TopicGroup.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            map.put(name, desc);
        }
        return map;
    }
}
