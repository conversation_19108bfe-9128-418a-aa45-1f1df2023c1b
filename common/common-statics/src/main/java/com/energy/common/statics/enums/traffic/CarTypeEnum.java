package com.energy.common.statics.enums.traffic;

import java.util.Arrays;
import java.util.List;

/**
 * @desc
 * @author: zhouf
 */
public enum CarTypeEnum {

    BIG(1, "大型汽车"),
    MINI(2, "小型汽车"),
    NEW_ENERGY_BIG(3, "新能源大型汽车"),
    NEW_ENERGY_MINI(4, "新能源小型汽车");


    private int code;
    private String desc;

    CarTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CarTypeEnum> toList() {
        return Arrays.asList(CarTypeEnum.values());
    }

    public static CarTypeEnum getEnum(int value) {
        CarTypeEnum resultEnum = null;
        CarTypeEnum[] enumAry = CarTypeEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getCode() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
