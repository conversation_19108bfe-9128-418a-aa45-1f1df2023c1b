package com.energy.common.statics.exceptions;

/**
 * 会员相关的异常 MemberException
 *
 * <AUTHOR>
 * @date 2019/12/9
 */
public class FlowMemberException extends BizException {
    /***
     * 系统异常
     */
    public static final int SYS_EXCEPTION = 109008001;
    /***
     * 业务异常
     */
    public static final int BIZ_EXCEPTION = 109008002;
    /**
     * 参数错误
     */
    public static final int PARAM_INVALID = 109008003;
    public static final int BATCH_ORDER_NO_REPEAT = 109008065;
    public static final int DETAIL_ORDER_NO_REPEAT = 109008066;

    public FlowMemberException() {
    }

    public FlowMemberException(String msg) {
        super(BIZ_EXCEPTION, msg);
    }

    public FlowMemberException(int sysErrCode, String msg) {
        super(sysErrCode, msg);
    }
}
