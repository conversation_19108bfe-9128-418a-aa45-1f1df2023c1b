package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化活动状态
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightActivityStatusTypeEnum {
    /**
     * 进行中
     */
    IN_PROGRESS(100,"进行中"),
    /**
     * 已结束(剩余数量为0或过期)
     */
    END(101,"已结束"),

    /**
     * 已下架
     */
    EXPIRED(102,"已下架"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightActivityStatusTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static LightWeightActivityStatusTypeEnum getEnum(int value) {
        LightWeightActivityStatusTypeEnum resultEnum = null;
        for (LightWeightActivityStatusTypeEnum typeEnum : LightWeightActivityStatusTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightActivityStatusTypeEnum> toList() {
        return Arrays.asList(LightWeightActivityStatusTypeEnum.values());
    }
}
