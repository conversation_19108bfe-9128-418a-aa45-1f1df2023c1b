package com.energy.common.statics.constants.common;

public class SecretKey {
    /**
     * 新代付用以非对称加解密的公私密钥，如：商户后台发起代付，页面JS用公钥加密，Action层用私钥解密
     */
    public static String PAYMENT_RSA_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC4OJRjhe26UdqeuzJuBzr/VNcnwb5RR9+KdCQYtPxrn+QRUZ1kXUcRzWjaR4SDHnhBsCrGVY9ksav2SovqKzXZHk/NLlRtA5XOtIiwz/DwD70Q8d0iZOxmZl2ODvIJiQVm1IiaXwDV3tfk2bzrdndNXCXn5SWDexCCkEjYALmcewIDAQAB";
    public static String PAYMENT_RSA_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALg4lGOF7bpR2p67Mm4HOv9U1yfBvlFH34p0JBi0/Guf5BFRnWRdRxHNaNpHhIMeeEGwKsZVj2Sxq/ZKi+orNdkeT80uVG0Dlc60iLDP8PAPvRDx3SJk7GZmXY4O8gmJBWbUiJpfANXe1+TZvOt2d01cJeflJYN7EIKQSNgAuZx7AgMBAAECgYBIqEgeWJxCeDY5lKt3KfLi8ZtKWuetNyO7f8Mc24UTDaTuD7p78y4sn09TiBGDhWj4v3qfbu93T3v7T7uPSCucIcgucaYWAiIAXtljdQRq1PiOM9DjEqBhdeiUZi0gWGvDm3czBM3T3RqYsyRgDuTNLaxlEQ0QIK67XD1q0TMLQQJBAPeh7pmxjUzrXyHjWYR9fnVhL1XKR+8zY3pYcel3io12GqMiZ9KN+IwwJQVpF+V5emOVqpHxIG42AnjIc8qOQdUCQQC+ciA/BTRpLSUBtlpmjLhsSpPClvHgu0TH8vFfim+3D+5jrxs28tF8+wtcPXOBGM1SKOSwcKvO02icDLiv5D0PAkEAuCF9hGTkl2xw2YbuujiCPo+cHeOCZ5fDgWaglSQhLxzBo8nif0q3pK3r6UEC1svkOIZCRbCRF8IrRlpu1w++XQJAFfdF3t7zz5nEZfEpdiCO+PaVGmGcnmYbJdDQpEtsXTn6Lgc5ZVoWbYrrIYV4+A/rRqmuzub/+ggKTc1t0+PIYwJAcBP8K5BC+ECb1g8HUDRMZfj8OXPPE2fli8TE4ZuvARF9O4esxcjoJz2uEgS0F+3nJUMoggKKKUBEYE6h/bf4pQ==";

    /**
     * 16位长度的日志AES加密的密钥(使用的地方有：新代付)
     */
    public final static String[] LOG_AES_KEY = {"EEory1RfM1SX6Vvm"};

    /**
     * 16位长度的新代付数据存储的AES加密的密钥
     */
    public final static String[] PAYMENT_DE_AES_KEY = {"","r2897GdQmxdvjTB0"};


    /**
     * 获取最尾的(即最新的)的密钥索引编号
     * @param keyArray
     * @return
     */
    public final static Integer getLastIndex(String[] keyArray){
        if(keyArray == null || keyArray.length == 0){
            return null;
        }else{
            return keyArray.length-1;
        }
    }
}
