package com.energy.common.statics.constants.common;

public class DistLockConst {
    /**
     * 是否采用数据库悲观锁
     */
    public final static boolean PESSIMIST_LOCK = false;
    // 账户操作锁
    public static final String ACCOUNT_OPERATE_LOCK_KEY = "ACCOUNT_OPERATE_LOCK_KEY-";
    //获取账务锁的等待时间
    public final static int ACCOUNT_LOCK_WAIT_MILLS = 20 * 1000;
    //账务锁的自动超时时间
    public final static int ACCOUNT_LOCK_EXPIRE_MILLS = 5 * 1000;
    // 交班操作锁
    public static final String SHIFT_OPERATE_LOCK_KEY = "SHIFT_OPERATE_LOCK_KEY-";
    // 获取交班锁等待时间
    public static final int SHIFT_OPERATE_LOCK_WAIT_MILLS = 30 * 1000;
    // 获取交班锁等待时间
    public static final int SHIFT_OPERATE_LOCK_EXPIRED_MILLS = 40 * 1000;
    // 车友村-权益领取操作锁
    public static final String FLOW_PRIORITY_GAIN_LINK_LOCK_KEY = "FLOW_PRIORITY_GAIN_LINK_LOCK_KEY-";
    // 获取车友村-权益领取操作锁的等待时间
    public final static int FLOW_PRIORITY_GAIN_LINK_LOCK_WAIT_MILLS = 20 * 1000;
    // 车友村-权益领取操作锁的自动超时时间
    public final static int FLOW_PRIORITY_GAIN_LINK_LOCK_EXPIRE_MILLS = 10 * 1000;
    // 进账分流计算操作锁
    public static final String FLOW_INCOME_SHUNT_COMPUTE_LOCK_KEY = "FLOW_INCOME_SHUNT_COMPUTE_LOCK_KEY-";
    // 进账分流计算操作锁的等待时间
    public final static int FLOW_INCOME_SHUNT_COMPUTE_LOCK_WAIT_MILLS = 20 * 1000;
    // 进账分流计算操作锁的自动超时时间
    public final static int FLOW_INCOME_SHUNT_COMPUTE_LOCK_EXPIRE_MILLS = 10 * 1000;
}
