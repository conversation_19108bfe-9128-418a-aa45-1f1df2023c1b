package com.energy.common.statics.exceptions;

import com.energy.common.statics.enums.common.BizCodeEnum;

/**
 * 支付相关的异常 PaymentException
 *
 * <AUTHOR>
 * @date 2019/12/9
 */
public class PaymentException extends BizException {
    /**
     * 路由不到可用的通道
     */
    public static final int NOT_CHANNEL = 102003001;
    /***
     * 加签、验签异常
     */
    public static final int SIGN_EXCEPTION = 102003002;
    /***
     * 请求通道返回异常
     */
    public static final int CHANNEL_EXCEPTION = 102003003;
    /**
     * 参数校验失败
     */
    public static final int PARAMS_DATA_VALIDATE_FAIL = 102003004;
    /**
     * 请求总笔数与受理总笔数不符
     */
    public static final int REQUEST_COUNT_NOT_MATCH_ACCEPT_COUNT = 102003005;
    /**
     * 请求总金额与受理总金额不符
     */
    public static final int REQUEST_AMOUNT_NOT_MATCH_ACCEPT_AMOUNT = 102003006;

    /***
     * 系统异常
     */
    public static final int SYS_EXCEPTION = 102003007;
    /***
     * 订单不存在
     */
    public static final int ORDER_NOT_EXIST = 102003008;

    /**
     * 通道服务异常
     */
    public static final int PAYMENT_CHANNEL_EXCEPTION = 102003008;
    /**
     * 非处理中状态
     */
    public static final int NOT_PROCESSING_STATUS = 102003010;
    /**
     * 未知状态
     */
    public static final int UNKNOW_STATUS = 102003011;
    /**
     * 不存在计费信息
     */
    public static final int NOT_EXIST_FEE_CALCULATE_RESULT = 102003012;

    public PaymentException() {
    }

    public PaymentException(int sysErrCode, String msg) {
        super(sysErrCode, msg);
    }

    public PaymentException(BizCodeEnum bizCodeEnum) {
        this.apiErrCode = bizCodeEnum.getCode();
        this.errMsg = bizCodeEnum.getMsg();
    }

    public PaymentException(String apiRespCode, String message) {
        this.apiErrCode = apiRespCode;
        this.errMsg = message;
    }
}
