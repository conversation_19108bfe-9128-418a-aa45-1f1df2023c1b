package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 协议签署接口版本
 */
public enum AgreementVersionEnum {
    V1("V1", "旧版"),
    V3("V3", "新版"),

    ;
    /**
     * 枚举值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    AgreementVersionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static AgreementVersionEnum getEnum(String value) {
        return Arrays.stream(values()).filter(p -> p.value.equals(value)).findFirst().orElse(null);
    }
}
