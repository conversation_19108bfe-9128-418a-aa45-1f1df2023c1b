package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 Integer 类型，校验规则为：不允许为null、参数值须介于 min ~ max 之间
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IntRangeValidator.class)
public @interface IntRange {
    String message() default "数值不在允许的区间范围内";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};

    /**
     * 最小值
     */
    int min() default Integer.MIN_VALUE;

    /**
     * 最大值
     */
    int max() default Integer.MAX_VALUE;
}
