package com.energy.common.statics.exceptions;

/**
 * 邮件发送异常
 */
public class EmailBizException extends BizException {

    /**
     * ------------------------- 邮件错误码，规范请参考新springBoot项目中BizException中描述的异常规范 --------------------------
     **/

    /**
     * 参数校验错误
     */
    public static final int PARAM_INVALID = 503001001;

    /**
     * 模板文件IO错误
     */
    public static final int TEMPLATE_IO_ERROR = 503003001;

    /**
     * 模板解析错误
     */
    public static final int TEMPLATE_ERROR = 503003002;

    /**
     * HTTP响应错误
     */
    public static final int HTTP_ERROR = 503003003;

    /**
     * 邮件类型为空
     */
    public static final int EMAIL_TYPE_NULL = 503003004;

    /**
     * 邮件主题为空
     */
    public static final int EMAIL_SUBJECT_NULL = 503003005;

    /**
     * 收件人邮箱为空
     */
    public static final int TO_MAIL_NULL = 503003006;

    /**
     * 邮件发送失败, 找不到对应的发送邮箱
     */
    public static final int NO_EMAIL_SENDER = 503003007;

    /**
     * 邮件发送异常
     */
    public static final int EMAIL_SEND_ERROR = 503003008;


    public EmailBizException(int code, String msg) {
        super(code, msg);
    }

    public EmailBizException(int code, String msg, Throwable e) {
        super(code, msg, e);
    }
}
