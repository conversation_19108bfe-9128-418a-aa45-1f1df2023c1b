package com.energy.common.statics.validation;

public class IntRangeValidator implements Validator<IntRange, Integer> {
	@Override
	public String validate(IntRange annotation, Integer value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		if (value == null) {
			return message;
		}
		int min = annotation.min();
		int max = annotation.max();
		if (min <= value && value <= max) {
			return null;
		} else {
			return message;
		}
	}
}
