package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化活动类型
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightActivityTypeEnum {
    /**
     * 消费达标
     */
    CONSUMPTION_UP_TO_STANDARD(1, "消费达标"),
    /**
     * 注册有礼
     */
    REGISTER_GIFT(2, "注册有礼"),

    /**
     * 会员日
     */
    MEMBER_DAY(3, "会员日"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightActivityTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightActivityTypeEnum getEnum(int value) {
        LightWeightActivityTypeEnum resultEnum = null;
        for (LightWeightActivityTypeEnum typeEnum : LightWeightActivityTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightActivityTypeEnum> toList() {
        return Arrays.asList(LightWeightActivityTypeEnum.values());
    }
}
