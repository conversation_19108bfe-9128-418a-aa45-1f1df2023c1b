package com.energy.common.statics.validation;

import java.math.BigDecimal;

public class DecimalRangeValidator implements Validator<DecimalRange, BigDecimal> {
	@Override
	public String validate(DecimalRange annotation, BigDecimal value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		if (value == null) {
			return message;
		}
		BigDecimal min = BigDecimal.valueOf(annotation.min());
		BigDecimal max = BigDecimal.valueOf(annotation.max());
		if (min.compareTo(value) <= 0 && value.compareTo(max) <= 0) {
			return null;
		} else {
			return message;
		}
	}
}
