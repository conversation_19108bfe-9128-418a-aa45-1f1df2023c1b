package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 协议发起人枚举值
 */
public enum AgreementPromoterEnum {
    JUNENG(1, "聚能数科"),
    MIGNXIANG(2, "鸣祥信息"),

    ;
    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String desc;

    AgreementPromoterEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static AgreementPromoterEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
