package com.energy.common.statics.enums.traffic;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车务订单状态
 */
public enum TrafficOrderStatusEnum {

    COMPLETE("已完成", 100),
    PAY_FAILED("支付失败", 101),
    UNPAID("待支付", 102),
    PROCESSING("办理中", 103),// 支付成功后，为办理中状态
    REFUNDING("退款中", 104),
    REFUNDED("已退款", 105),
//    CANCELED("已取消", 106)
    ;

    /** 枚举值 */
    private int value;
    /** 描述 */
    private String desc;

    TrafficOrderStatusEnum(String desc, int value) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static TrafficOrderStatusEnum getEnum(int value) {
        TrafficOrderStatusEnum resultEnum = null;
        for (TrafficOrderStatusEnum areaTypeEnum : TrafficOrderStatusEnum.values()) {
            if (areaTypeEnum.getValue() == value) {
                resultEnum = areaTypeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<TrafficOrderStatusEnum> toList(){
        return Arrays.asList(TrafficOrderStatusEnum.values());
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> enumMap = new HashMap<>();
        for (TrafficOrderStatusEnum areaTypeEnum : TrafficOrderStatusEnum.values()) {
            enumMap.put(areaTypeEnum.value, areaTypeEnum.desc);
        }
        return enumMap;
    }

    /**
     * 车主端违章历史记录中可展示的记录状态
     *
     * @return
     */
    public static List<Integer> toHistoryRecordStatusList() {
        return Arrays.asList(
                COMPLETE.getValue(),
                PROCESSING.getValue(),
                REFUNDED.getValue(),
                REFUNDING.getValue()
        );
    }
}
