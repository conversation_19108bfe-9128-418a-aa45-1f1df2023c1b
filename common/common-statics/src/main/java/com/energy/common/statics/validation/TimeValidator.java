package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

public class TimeValidator implements Validator<Time, String> {

	@Override
	public String validate(Time annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		return (ValidUtil.isNotEmpty(value) && ValidUtil.isTime(value)) ? null : annotation.message();
	}
}
