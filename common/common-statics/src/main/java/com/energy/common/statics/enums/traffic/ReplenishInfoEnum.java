package com.energy.common.statics.enums.traffic;

/**
 * @desc 补充资料Map
 * @author: zhouf
 */
public enum ReplenishInfoEnum {

    /**
     *
     */
    VIN("a", "行驶证车架号"),
    VIN4("a4", "行驶证车架号（后4位）"),
    VIN6("a6", "行驶证车架号（后6位）"),
    ENGINE_NO("b", "行驶证发动机号（全位）"),
    ENGINE_NO4("b4", "行驶证发动机号（后4位）"),
    ENGINE_NO6("b6", "行驶证发动机号（后6位）"),
    VEHICLE_NAME("c", "行驶证车主姓名"),
    VEHICLE_PHONE("d", "行驶证绑定电话"),
    VEHICLE_FILE_NO("cg", "行驶证档案编号"),
    DRIVING_NAME("e", "驾照姓名"),
    DRIVING_PHONE("f", "驾驶证绑定电话"),
    DRIVING_FILE_NO("g", "驾驶证档案编号"),
    DRIVING_NO("h", "驾驶证号"),
    DRIVING_CORE_NO("i", "驾驶证芯编号");

    private String code;

    private String desc;

    ReplenishInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReplenishInfoEnum getEnum(String code) {
        ReplenishInfoEnum resultEnum = null;
        ReplenishInfoEnum[] enumAry = ReplenishInfoEnum.values();
        for (ReplenishInfoEnum methodEnum : enumAry) {
            if (methodEnum.getCode().equals(code)) {
                resultEnum = methodEnum;
                break;
            }
        }
        return resultEnum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
