package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化权益类型
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightPriorityTypeEnum {
    /**
     * 满减劵
     */
    FULL_REDUCE_COUPON(1, "满减劵"),
    /**
     * 折扣
     */
    DISCOUNT(2, "折扣"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightPriorityTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightPriorityTypeEnum getEnum(int value) {
        LightWeightPriorityTypeEnum resultEnum = null;
        for (LightWeightPriorityTypeEnum typeEnum : LightWeightPriorityTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightPriorityTypeEnum> toList() {
        return Arrays.asList(LightWeightPriorityTypeEnum.values());
    }
}
