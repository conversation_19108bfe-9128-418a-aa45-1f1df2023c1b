package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

/**
 * 金额校验
 */
public class AmountValidator implements Validator<Amount, String> {

	@Override
	public String validate(Amount annotation, String value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		return ValidUtil.isNotEmpty(value) && ValidUtil.isAmount(value) ? null : annotation.message();
	}
}
