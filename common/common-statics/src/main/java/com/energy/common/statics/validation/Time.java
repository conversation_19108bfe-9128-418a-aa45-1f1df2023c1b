package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不允许为null、参数值须符合 HH:mm:ss 的格式
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TimeValidator.class)
public @interface Time {
	String message() default "参数值不是正确的时间格式";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};
}