package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户类型
 *
 * <AUTHOR>
 * @date 2020/04/26 16:19
 */
public enum MchTypeEnum {

    OIL_STATION(1, "加油站"),

    GROUP(2, "集团"),

    OP_CAR_TEAM(3, "采购车队"),

    PUR_CAR_TEAM(4, "运营车队"),

    FUND_MCH(5, "资金方"),

    SERVICE_MCH(6, "服务商"),

    INSURANCE_MCH(7, "保险商户"),

    INSURANCE_AGENT_MCH(8, "保险代理商户"),

    ;

    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    MchTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static MchTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
