package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化权益状态
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightPriorityStatusTypeEnum {
    /**
     * 正常
     */
    IN_PROGRESS(100,"正常"),

    /**
     * 已领完(库存数量为0或过期)
     */
    END(101,"已领完"),

    /**
     * 已下架
     */
    EXPIRED(102,"已下架"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightPriorityStatusTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static LightWeightPriorityStatusTypeEnum getEnum(int value) {
        LightWeightPriorityStatusTypeEnum resultEnum = null;
        for (LightWeightPriorityStatusTypeEnum typeEnum : LightWeightPriorityStatusTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightPriorityStatusTypeEnum> toList() {
        return Arrays.asList(LightWeightPriorityStatusTypeEnum.values());
    }
}
