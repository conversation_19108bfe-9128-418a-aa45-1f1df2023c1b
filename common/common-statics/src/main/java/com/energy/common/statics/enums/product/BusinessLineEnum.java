package com.energy.common.statics.enums.product;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务线枚举
 */
public enum BusinessLineEnum {
    JAYOCUN_1_0(1, "加油村1.0"),
    JAYOCUN_2_0(2, "加油村2.0"),
    CAR_INSURANCE(3, "车险服务"),
    BROKERAGE_1_0(4,"营销推广"),
    BROKERAGE_2_0(5, "灵工发放"),

    ;

    /** 枚举值 */
    private int value;

    /** 描述 */
    private String desc;

    private BusinessLineEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BusinessLineEnum getEnum(int value) {
        BusinessLineEnum resultEnum = null;
        BusinessLineEnum[] enumAry = BusinessLineEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getValue() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public static List<Map<String, String>> toList() {
        BusinessLineEnum[] ary = BusinessLineEnum.values();
        List<Map<String, String>> list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }
}
