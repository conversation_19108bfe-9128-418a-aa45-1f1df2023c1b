package com.energy.common.statics.enums.oilcard;

import com.energy.common.statics.annotations.DictIgnore;
import jdk.nashorn.internal.ir.annotations.Ignore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OilCardOrderTypeEnum 油卡订单类型（订单管理用的）
 *
 * <AUTHOR>
 * @date 2020/8/6
 */
@DictIgnore
public enum OilCardOrderTypeEnum {
    RECHARGE("recharge", "充值"),
    CONSUME("consume", "消费"),
    CANCEL("cancel", "撤销"),
    ;
    /**
     * 枚举值
     */
    private String value;
    /**
     * 描述
     */
    private String msg;

    private final static Map<String, String> VALUE_MAP = toValueMap();


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    OilCardOrderTypeEnum(String value, String desc) {
        this.value = value;
        this.msg = desc;
    }

    public static Map<String, String> toValueMap() {
        OilCardOrderTypeEnum[] arr = OilCardOrderTypeEnum.values();

        Map<String, String> map = new HashMap<>(arr.length);
        for (int i = 0; i < arr.length; i++) {
            map.put(arr[i].getValue(), arr[i].getMsg());
        }
        return map;
    }

    public static Map<String, String> getValueMap() {
        return VALUE_MAP;
    }

    public static int getIntValue(OilCardOrderTypeEnum signType) {
        return Integer.valueOf(signType.getValue());
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static List toList() {
        OilCardOrderTypeEnum[] ary = OilCardOrderTypeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getMsg());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据枚举值获取枚举属性.
     *
     * @param value 枚举值.
     * @return enum 枚举属性.
     */
    public static OilCardOrderTypeEnum getEnum(String value) {
        OilCardOrderTypeEnum resultEnum = null;
        OilCardOrderTypeEnum[] enumAry = OilCardOrderTypeEnum.values();
        for (OilCardOrderTypeEnum typeEnum : enumAry) {
            if (typeEnum.getValue().equals(value)) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }
}
