package com.energy.common.statics.exceptions;

/**
 * @description 交易异常类
 * 前面三位数字定义为 103。详细请查看 {@link com.energy.common.exception.BizException}中的定义。
 * 后三位定义如下：
 * 1xx ： 充值
 * 2xx :  消费
 * @author: zhouf
 * @date: 2019/12/9
 */
public class TradeBizException extends BizException {

    /**
     * 三方通道公用异常
     */
    public static int THIRD_PLAT_COMMON_ERR = **********;

    /**
     * 三方通道系统异常
     */
    public static int THIRD_PLAT_SYSTEM_ERR = **********;

    /**
     * 三方通道业务异常
     */
    public static int THIRD_PLAT_BIZ_ERR = **********;

    /**
     * 账务处理异常
     */
    public static int ACCOUNT_PROCESS_ERR_CODE = **********;

    /**
     * 通用异常
     **/
    public static final TradeBizException UNKNOWN_ERR = new TradeBizException(**********, "油卡交易业务未知错误");
    public static final TradeBizException DUPLICATE_ORDER_NO = new TradeBizException(**********, "支付业务订单已存在");
    public static final TradeBizException PAY_CHANNEL_ERR = new TradeBizException(**********, "支付通道发生异常");
    public static final TradeBizException PAY_CHNNEL_QRY_ERR = new TradeBizException(**********, "查询支付通道订单详情异常");
    public static final TradeBizException MCH_NO_NOT_EXITS = new TradeBizException(**********, "商户不存在");
    public static final TradeBizException MCH_NO_NO_ACTIVE = new TradeBizException(**********, "商户未激活");
    public static final TradeBizException PRE_PAY_ACCOUNT_NOT_EXIST = new TradeBizException(**********, "车主账户信息异常");
    public static final TradeBizException PRE_PAY_ACCOUNT_INVALIDE = new TradeBizException(**********, "车主账户已被冻结");
    public static final TradeBizException PRE_RECEIVE_ACCOUNT_NOT_EXIST = new TradeBizException(**********, "商户未开通收付款账户");
    public static final TradeBizException PRE_RECEIVE_ACCOUNT_INVALIDE = new TradeBizException(**********, "商户预收款账户未激活");

    /**
     * 消费异常start
     **/
    public static final TradeBizException CONSUME_RECORD_IS_NOT_EXIST = new TradeBizException(*********, "消费记录不存在");
    public static final TradeBizException CONSUME_STATUS_ERROR = new TradeBizException(*********, "消费记录状态错误");
    /** 消费异常end **/

    /**
     * 充值异常start
     **/
    public static final TradeBizException EMPTY_RESULT = new TradeBizException(**********, "充值支付通道返回为空");
    public static final TradeBizException ORDER_RECORD_NOT_EXIST = new TradeBizException(**********, "充值订单支付记录不存在");
    public static final TradeBizException ORDER_NOT_EXIST = new TradeBizException(**********, "充值订单不存在");
    public static final TradeBizException ORDER_AMOUNT_ERROR = new TradeBizException(**********, "订单金额错误");
    /** 充值异常end **/

    /**
     * 支付异常start
     **/
    public static final TradeBizException PAY_CHNNEL_EMPTY_RESULT = new TradeBizException(1031004100, "支付通道返回为空");
    public static final TradeBizException PAY_ORDER_RECORD_NOT_EXIST = new TradeBizException(**********, "支付订单支付记录不存在");
    public static final TradeBizException PAY_ORDER_NOT_EXIST = new TradeBizException(**********, "支付订单不存在");
    public static final TradeBizException PAY_ORDER_AMOUNT_ERROR = new TradeBizException(**********, "支付订单金额错误");
    public static final TradeBizException PAY_WAY_ERR = new TradeBizException(**********, "支付方式错误");
    public static final TradeBizException PAY_PRODUCT_CODE_ERR = new TradeBizException(1031004105, "支付产品编号错误");
    public static final TradeBizException PAY_ORDER_END_STATUS = new TradeBizException(1031004106, "支付订单已为终态");
    public static final TradeBizException PAY_PLAT_TRX_NO_ERR = new TradeBizException(1031004107, "未知的平台流水号");
    public static final TradeBizException UNKNOWN_CHANNEL = new TradeBizException(1031004108, "未知的通道");
    public static final TradeBizException AMOUNT_TOO_LOW = new TradeBizException(1031004109, "订单金额过低");
    public static final TradeBizException THIRD_PLAT_EMPTY_RESULT = new TradeBizException(**********, "第三方平台通道返回为空");
    public static final TradeBizException PAY_ORDER_STATUS_ERROR = new TradeBizException(1031004111, "支付订单状态错误");
    public static final TradeBizException CHANNEL_TERMINAL_NO_NOT_EXIST = new TradeBizException(1031004112, "通道终端编号不存在");
    public static final TradeBizException PAY_RECORD_NOT_FINISH = new TradeBizException(1031004113, "支付记录未完成");
    /** 支付异常end **/

    /** 车队交易异常start **/
    public static final TradeBizException PRODUCT_NOT_OPEN = new TradeBizException(1031005100, "产品未开通");
    public static final TradeBizException PURCHASE_IS_NOT_EXIST = new TradeBizException(1031005101, "消费记录不存在");
    public static final TradeBizException PURCHASE_STATUS_ERROR = new TradeBizException(1031005102, "消费记录状态错误");
    public static final TradeBizException TEAM_OIL_CARD_NOT_EXIST = new TradeBizException(1031005103, "车队油卡不存在");
    public static final TradeBizException TEAM_OIL_CARD_STATUS_ERROR = new TradeBizException(1031005104, "车队油卡已被冻结");
    public static final TradeBizException CAR_TEAM_OWNER_STATUS_ERROR = new TradeBizException(1031005105, "车队与车主关联状态异常");
    public static final TradeBizException CAR_TEAM_OWNER_RELATION_EXIST = new TradeBizException(**********, "车队与车主关联关系异常");
    public static final TradeBizException CAR_TEAM_OWNER_QUOTA_ZERO = new TradeBizException(**********, "配额额度为0");
    public static final TradeBizException CAR_TEAM_OWNER_QUOTA_EXCESS = new TradeBizException(**********, "配额额度超过车队额度");
    public static final TradeBizException CAR_TEAM_OWNER_ACCOUNT_ERROR = new TradeBizException(**********, "账号异常");
    public static final TradeBizException PURCHASE_ACCOUNT_ERROR = new TradeBizException(**********, "采购交易，入账失败");
    public static final TradeBizException PURCHASE_MCH_ACCOUNT_ERROR = new TradeBizException(**********, "采购交易，商户入账失败");
    public static final TradeBizException QUOTA_ACCOUNT_ERROR = new TradeBizException(**********, "配额交易，入账失败");
    public static final TradeBizException QUOTA_CARER_ACCOUNT_ERROR = new TradeBizException(**********, "配额交易，车主入账失败");
    public static final TradeBizException PERSON_OIL_CARD_STATUS_ERROR = new TradeBizException(**********, "车主油卡状态异常");
    public static final TradeBizException CAR_TEAM_STATUS_ERROR = new TradeBizException(**********, "车队状态异常");
    public static final TradeBizException CAR_TEAM_NOT_EXIST = new TradeBizException(**********, "车队不存在");
    public static final TradeBizException CAR_TEAM_RELATION_ERROR = new TradeBizException(**********, "车队绑定关系异常");
    public static final TradeBizException PERSON_OIL_CARD_NOT_EXIST = new TradeBizException(**********, "车主油卡不存在");
    public static final TradeBizException CAED_TYPE_ERROR = new TradeBizException(**********, "油卡类型不匹配");

    /** 车队交易异常end **/

    /** 提现订单相关 **/
    public static final TradeBizException GRANT_ORDER_EXIST = new TradeBizException(**********, "转让订单号已存在");
    public static final TradeBizException GRANT_ORDER_NOT_EXIST = new TradeBizException(**********, "商户订单不存在");
    public static final TradeBizException GRANT_UNKNOWN_ERR = new TradeBizException(**********, "未知异常");
    public static final TradeBizException NOT_SUFFICIENT_FUNDS = new TradeBizException(**********, "余额不足");
    public static final TradeBizException ACCOUNT_NOT_EXIST = new TradeBizException(**********, "未开通账户");
    public static final TradeBizException NATIVE_ACCOUNT_PROCESS_ERR = new TradeBizException(**********, "本地账务处理异常");

    /** 资金方交易异常 **/
    public static final TradeBizException FUND_ACCOUNT_ERROR = new TradeBizException(**********, "资金方账务处理失败");

    public TradeBizException(int code, String msg) {
        super(code, msg);
    }

    public TradeBizException(String msg) {
        super(COMMON_ERROR_CODE, msg);
    }

    public TradeBizException() {
        super();
    }
}
