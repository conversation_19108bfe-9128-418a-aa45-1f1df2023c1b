package com.energy.common.statics.enums.message;

import com.energy.common.statics.annotations.DictIgnore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SmsBusinessTypeEnum 短信业务类型枚举
 *
 * <AUTHOR>
 * @date 2020/6/29
 */
@DictIgnore
public enum SmsBusinessTypeEnum {
    REGISTER_MEMBER("注册会员", 1),
    MOIDFY_PHONE_NO("修改手机号", 2),
    WITHDRAW_VERIFY_CODE("提现验证码", 3),
    ;

    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String desc;

    SmsBusinessTypeEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static SmsBusinessTypeEnum getEnum(int value) {
        SmsBusinessTypeEnum resultEnum = null;
        SmsBusinessTypeEnum[] enumAry = values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getValue() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public static Map<String, Map<String, Object>> toMap() {
        SmsBusinessTypeEnum[] ary = values();
        Map<String, Map<String, Object>> enumMap = new HashMap<>();
        for (int num = 0; num < ary.length; num++) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(ary[num].getValue()));
            map.put("value", String.valueOf(ary[num].getValue()));
            map.put("desc", ary[num].getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }

    public static List toList() {
        SmsBusinessTypeEnum[] ary = values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

}
