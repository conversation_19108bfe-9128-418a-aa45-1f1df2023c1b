package com.energy.common.statics.enums.message;

public enum TTSPlatformEnum {
    ALI_CLOUD(1, "阿里云"),
    TENCENT(2, "腾讯云"),
    ;


    private int value;
    private String desc;

    private TTSPlatformEnum(int value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static TTSPlatformEnum getEnum(Integer value){
        TTSPlatformEnum[] enumAry = TTSPlatformEnum.values();
        for(int i = 0;i<enumAry.length;i++){
            if(enumAry[i].getValue() == value){
                return enumAry[i];
            }
        }
        return null;
    }
}
