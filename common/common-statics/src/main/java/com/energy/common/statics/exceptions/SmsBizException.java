package com.energy.common.statics.exceptions;

/**
 * 短信发送异常
 */
public class SmsBizException extends BizException {

    /**
     * ------------------------- 短信错误码，规范请参考新springBoot项目中BizException中描述的异常规范 --------------------------
     **/

    /**
     * 参数校验错误
     */
    public static final int PARAM_INVALID = 503001001;

    /**
     * 短信流水号重复
     */
    public static final int DUPLICATE_TRX_NO = 503001002;

    /**
     * 短信记录创建错误
     */
    public static final int CREATE_RECORD_ERROR = 503001003;

    /**
     * 模板文件IO错误
     */
    public static final int TEMPLATE_IO_ERROR = 503003001;

    /**
     * 模板解析错误
     */
    public static final int TEMPLATE_ERROR = 503003002;

    /**
     * HTTP响应错误
     */
    public static final int HTTP_ERROR = 503003003;

    //
    /**
     * 梦网短信接口请求错误
     */
    public static final int MONGATE_ERROR = 503003004;

    /**
     * 智众联短信接口请求错误
     */
    public static final int ZHIZL_ERROR = 503003005;

    /**
     * 短信通道路由配置错误
     */
    public static final int SMS_CHANNEL_ROUTE_ERROR = 503003006;

    /**
     * 云信短信接口请求错误
     */
    public static final int YUNXIN_ERROR = 503003007;

    /**
     * 手机号为空
     */
    public static final int PHONE_NULL = 503003008;

    /**
     * 短信类型为空
     */
    public static final int SMS_TYPE_NULL = 503003009;


    public SmsBizException(int code, String msg) {
        super(code, msg);
    }

    public SmsBizException(int code, String msg, Throwable e) {
        super(code, msg, e);
    }
}
