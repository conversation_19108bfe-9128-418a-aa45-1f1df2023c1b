package com.energy.common.statics.enums.message;

import com.energy.common.statics.annotations.DictIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * PushPlatformTypeEnum 推送平台类型
 *
 * <AUTHOR>
 * @date 2020/3/10
 */
@DictIgnore
public enum PushPlatformTypeEnum {
	ANDROID("安卓", "android"),
	IOS("IOS", "ios"),
	ANDROID_IOS("安卓和IOS", "android_ios"),
	;
	/**
	 * 描述值
	 */
	private String desc;

	/**
	 * 值
	 */
	private String value;

	PushPlatformTypeEnum(String desc, String value) {
		this.desc = desc;
		this.value = value;
	}

	public static Map<String, PushPlatformTypeEnum> getMap() {
		Map<String, PushPlatformTypeEnum> map = new HashMap<>();
		for (PushPlatformTypeEnum pushPlatformTypeEnum : PushPlatformTypeEnum.values()) {
			map.put(pushPlatformTypeEnum.getValue(), pushPlatformTypeEnum);
		}
		return map;
	}

	public String getDesc() {
		return desc;
	}

	public String getValue() {
		return value;
	}
}
