package com.energy.common.statics.validation;

/**
 * 金额校验
 */
public class LengthValidator implements Validator<Length, CharSequence> {
	@Override
	public String validate(Length annotation, CharSequence value, String scene) {
		if (isSceneNotMatch(annotation.scenes(), scene)) {
			return null;
		}
		String message = annotation.message();
		if (value == null) {
			return message;
		}
		int min = annotation.min();
		int max = annotation.max();
		int length = value.length();
		if (min <= length && length <= max) {
			return null;
		} else {
			return message;
		}
	}
}
