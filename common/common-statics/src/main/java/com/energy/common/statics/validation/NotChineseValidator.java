package com.energy.common.statics.validation;

import com.energy.common.statics.util.ValidUtil;

/**
 * 中文校验，如果包含中文字符，就会校验不通过
 */
public class NotChineseValidator implements Validator<NotChinese, String> {

    @Override
    public String validate(NotChinese annotation, String value, String scene) {
        if (isSceneNotMatch(annotation.scenes(), scene)) {
            return null;
        }
        return !ValidUtil.isChinese(value) ? null : annotation.message();
    }
}
