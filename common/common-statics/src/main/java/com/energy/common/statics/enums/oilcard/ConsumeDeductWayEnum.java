package com.energy.common.statics.enums.oilcard;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: xiebochang
 * @Title: ConsumeDeductWayEnum
 * @date: 2020/8/19
 */
public enum ConsumeDeductWayEnum {

    CARD_TEAM_ACCOUNT("（优先）车队预付款账户", 0),
    PERSON_CARD_ACCOUNT("子卡/个人油卡预付款账户", 1),
    ;

    /** 枚举值 */
    private int value;
    /** 描述 */
    private String desc;

    ConsumeDeductWayEnum (String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static ConsumeDeductWayEnum getEnum(int value) {
    	ConsumeDeductWayEnum resultEnum = null;
    	ConsumeDeductWayEnum[] enumAry = ConsumeDeductWayEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getValue() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public static Map<String, Map<String, Object>> toMap() {
    	ConsumeDeductWayEnum[] ary = ConsumeDeductWayEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<>();
        for (int num = 0; num < ary.length; num++) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(ary[num].getValue()));
            map.put("value", String.valueOf(ary[num].getValue()));
            map.put("desc", ary[num].getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }


    public static List toList() {
    	ConsumeDeductWayEnum[] ary = ConsumeDeductWayEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }
}
