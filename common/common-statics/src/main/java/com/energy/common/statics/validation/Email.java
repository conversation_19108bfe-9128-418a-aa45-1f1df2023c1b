package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不允许为null、符合邮箱格式************
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EmailValidator.class)
public @interface Email {
	String message() default "邮箱地址格式错误";

	/**
	 * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
	 */
	String[] scenes() default {};
}