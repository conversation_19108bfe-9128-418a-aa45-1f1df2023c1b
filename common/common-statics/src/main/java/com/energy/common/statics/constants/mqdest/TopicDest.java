package com.energy.common.statics.constants.mqdest;

import com.energy.common.statics.annotations.Queue;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * MQ消息目的地，在ActiveMQ中是Queue名称或虚拟队列名称，在RocketMQ中就是Topic名称，可在消息消费端被业务方用以逻辑划分
 */
public class TopicDest {

    @Queue(desc = "智能付发起单笔提现完成通知", group = TopicGroup.REMIT_GROUP)
    public final static String AUTO_REMIT_SINGLE_COMPLETE = "energy.auto.remit.single.complete";
    @Queue(desc = "发送异步邮件", group = TopicGroup.EMAIL_GROUP)
    public static final String EMAIL_SEND_ASYNC = "email.async";
    @Queue(desc = "发送合并邮件", group = TopicGroup.EMAIL_GROUP)
    public static final String EMAIL_MERGE_SEND = "email.merge.send";
    @Queue(desc = "异步移动推送", group = TopicGroup.COMMON_GROUP)
    public static final String MOBILE_PUSH_ASYNC = "common.mobilePushAsync";
    @Queue(desc = "支付补单", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAYMENT_REISSUE = "energy.payment.reissue";
    @Deprecated
    @Queue(desc = "支付结果查询", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAYMENT_RESULT_QUERY = "energy.payment.result.query";
    @Queue(desc = "支付结果查询", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAY_RECORD_RESULT_QUERY = "pay.record.queryResult";
    @Queue(desc = "支付记录完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAY_RECORD_COMPLETE = "pay.record.complete";

    //收单系统
    @Deprecated
    @Queue(desc = "通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAYMENT_CHANNEL_COMPLETE = "energy.payment.channel.complete";
    @Queue(desc = "通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAY_RECORD_CHANNEL_COMPLETE = "pay.record.channel.complete";

    @Deprecated
    @Queue(desc = "订单支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAYMENT_COMPLETE = "VirtualTopic.energy.payment.complete";
    @Queue(desc = "订单支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String PAY_ORDER_COMPLETE = "VirtualTopic.pay.order.complete";

    @Queue(desc = "商户通知回调(公用)", group = TopicGroup.PAYMENT_GROUP)
    public static final String MERCHANT_NOTIFY_COMMON = "merchant.notify.common";

    @Queue(desc = "通知账务处理", group = TopicGroup.ACCOUNT_GROUP)
    public static final String ACCOUNT_DO_ACCOUNT_PROCESS_QUEUE_NAME = "queue.notify.account.self.process";

    @Queue(desc = "通知账务回调", group = TopicGroup.ACCOUNT_GROUP)
    public final static String ACCOUNT_DO_ACCOUNT_CALLBACK_QUEUE_NAME = "queue.notify.account.self.callback";

    @Queue(desc = "消费账务处理完成通知", group = TopicGroup.ACCOUNT_GROUP)
    public static final String CONSUME_ACCOUNT_PROCESS_COMPLETE = "consume.account.process.complete";

    @Queue(desc = "消费撤销账务处理完成通知", group = TopicGroup.ACCOUNT_GROUP)
    public final static String CONSUME_CANCEL_ACCOUNT_PROCESS_COMPLETE = "consume.cancel.account.process.complete";

    @Queue(desc = "上线发布", group = TopicGroup.DEV_OPS)
    public final static String DEV_OPS_PUBLISH_PROJECT = "devops.publish.project";

    @Queue(desc = "收银系统交班通知重新出款", group = TopicGroup.SHIFT_GROUP)
    public final static String SHIFT_REPEAT_REMIT_QUEUE = "energy.shift.repeat.remit";

    @Queue(desc = "api交班通知重新出款", group = TopicGroup.SHIFT_GROUP)
    public final static String API_SHIFT_REPEAT_REMIT_QUEUE = "energy.api.shift.repeat.remit";

    @Queue(desc = "api交班通知统计并出款-新交班", group = TopicGroup.SHIFT_GROUP)
    public final static String API_SHIFT_COUNT_AND_REMIT_QUEUE_NEW = "energy.api.shift.count.and.remit.new";

    @Queue(desc = "api交班通知统计并出款", group = TopicGroup.SHIFT_GROUP)
    public final static String API_SHIFT_COUNT_AND_REMIT_QUEUE = "energy.api.shift.count.and.remit";

    @Deprecated
    @Queue(desc = "通道退款完成", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_CHANNEL_COMPLETE = "energy.refund.channel.complete";
    @Deprecated
    @Queue(desc = "通道退款完成-易加油", group = TopicGroup.REFUND_GROUP)
    public static final String EJIAYOU_REFUND_CHANNEL_COMPLETE = "energy.ejiayou.refund.channel.complete";
    @Deprecated
    @Queue(desc = "订单退款完成", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_COMPLETE = "VirtualTopic.energy.refund.complete";

    @Queue(desc = "通道退款完成", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_RECORD_CHANNEL_COMPLETE = "refund.channel.complete";
    @Queue(desc = "退款记录完成", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_RECORD_COMPLETE = "refund.record.complete";
    @Queue(desc = "订单退款完成", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_ORDER_COMPLETE = "VirtualTopic.refund.order.complete";

    @Queue(desc = "易加油订单退款完成", group = TopicGroup.EJIAYOU_REFUND_GROUP)
    public static final String EJIAYOU_REFUND_COMPLETE = "VirtualTopic.ejiayou.refund.complete";

    @Queue(desc = "轻量化订单退款完成", group = TopicGroup.LIGHT_MCH_REFUND_GROUP)
    public static final String LIGHT_MCH_REFUND_COMPLETE = "VirtualTopic.lightMch.refund.complete";

    @Queue(desc = "本地订单退款完成", group = TopicGroup.REFUND_GROUP)//现金、银行卡
    public static final String NATIVE_REFUND_COMPLETE = "VirtualTopic.native.energy.refund.complete";

    @Queue(desc = "退款补单", group = TopicGroup.REFUND_GROUP)
    public static final String REFUND_REISSUE = "energy.refund.reissue";

    @Queue(desc = "易加油退款补单", group = TopicGroup.EJIAYOU_REFUND_GROUP)
    public static final String EJIAYOU_REFUND_REISSUE = "energy.ejiayou.refund.reissue";

    //充值
    @Queue(desc = "通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String RECHARGE_CHANNEL_COMPLETE = "energy.recharge.channel.complete";

    @Queue(desc = "订单充值完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String RECHARGE_COMPLETE = "VirtualTopic.energy.recharge.complete";

    @Queue(desc = "充值补单", group = TopicGroup.PAYMENT_GROUP)
    public static final String RECHARGE_REISSUE = "energy.recharge.reissue";

    //收款
    @Queue(desc = "通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String TRADE_CHANNEL_COMPLETE = "energy.trade.channel.complete";

    @Queue(desc = "订单收款完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String TRADE_COMPLETE = "VirtualTopic.energy.trade.complete";

    @Queue(desc = "收款补单", group = TopicGroup.PAYMENT_GROUP)
    public static final String TRADE_REISSUE = "energy.trade.reissue";

    // 分账收款 start
    @Queue(desc = "收款补单", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_REISSUE = "energy.altTrade.reissue";
    @Queue(desc = "通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_CHANNEL_COMPLETE = "energy.altTrade.channel.complete";
    @Queue(desc = "订单收款完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_COMPLETE = "VirtualTopic.energy.altTrade.complete";
    @Queue(desc = "易加油平台通知补单", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_THIRD_PLAT_NOTIFY_REISSUE = "energy.altTradeThirdPlatNotify.reissue";
    @Queue(desc = "流量管家分销佣金允许提现", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_ALLOW_AWARD_WITHDRAW = "energy.altTrade.allowAwardWithDraw";
    @Queue(desc = "流量管家分销佣金允许提现", group = TopicGroup.FLOW_MARKETING_GROUP)
    public static final String FLOW_PRIORITY_TIMED_TASK = "energy.flow.priority.timedTask";
    @Queue(desc = "支付结果自动查询", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_PAY_RESULT_QUERY = "altTrade.pay.queryResult";
    @Queue(desc = "支付记录统计", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_PAY_RECORD_STATISTIC = "altTrade.payRecord.statistic";
    // end

    // 车务 start
    @Queue(desc = "车务订单通道支付完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String TRAFFIC_TRADE_CHANNEL_COMPLETE = "energy.traffic.trade.channel.complete";
    @Queue(desc = "车务订单收款完成", group = TopicGroup.TRAFFIC_GROUP)
    public static final String TRAFFIC_TRADE_COMPLETE = "VirtualTopic.energy.trafficTrade.complete";
    @Queue(desc = "车务订单支付补单", group = TopicGroup.TRAFFIC_GROUP)
    public static final String TRAFFIC_TRADE_REISSUE = "energy.trafficTrade.reissue";
    @Queue(desc = "车车网平台支付成功通知补单", group = TopicGroup.TRAFFIC_GROUP)
    public static final String NOTIFY_CCW_REISSUE = "energy.notifyCcwAfterPayComplete.reissue";
    // 车务退款
    @Queue(desc = "车务订单通道退款完成", group = TopicGroup.REFUND_GROUP)
    public static final String TRAFFIC_REFUND_CHANNEL_COMPLETE = "energy.traffic.refund.channel.complete";
    @Queue(desc = "车务订单退款完成", group = TopicGroup.TRAFFIC_GROUP)
    public static final String TRAFFIC_REFUND_COMPLETE = "VirtualTopic.traffic.refund.complete";
    @Queue(desc = "车务订单退款补单", group = TopicGroup.TRAFFIC_GROUP)
    public static final String TRAFFIC_REFUND_REISSUE = "energy.traffic.refund.reissue";
    // 车务 end

    @Queue(desc = "商户回调常规重试", group = TopicGroup.COMMON_GROUP)
    public static final String MERCHANT_NOTIFY_NORMAL_RETRY = "merchant.notify.normal.retry";
    @Queue(desc = "商户回调串行化通知", group = TopicGroup.COMMON_GROUP)
    public static final String MERCHANT_NOTIFY_SERIALIZATION = "merchant.notify.serialization";

    @Queue(desc = "订单消费完成", group = TopicGroup.CONSUME_GROUP)
    public static final String CONSUME_COMPLETE = "VirtualTopic.energy.consume.complete";

    @Queue(desc = "消费码消费完成", group = TopicGroup.CONSUME_GROUP)
    public static final String CONSUME_CODE_COMPLETE = "energy.consume.code.complete";

    @Queue(desc = "微信卡包-请求微信拉取会员信息", group = TopicGroup.MEMBER_GROUP)
    public final static String MEMBER_WEI_XIN_USER_INFO = "member.wei.xin.user.info";

    @Queue(desc = "权益维护定时任务队列", group = TopicGroup.MEMBER_GROUP)
    public static final String MEMBER_PRIORITY_TASK = "member.priority.task";

    @Queue(desc = "油品调价定时任务队列", group = TopicGroup.COMMON_GROUP)
    public static final String PRICE_ADJUST_TASK = "price.adjust.task";

    @Queue(desc = "会员营销活动维护定时任务队列", group = TopicGroup.MEMBER_GROUP)
    public static final String MEMBER_ACTIVITY_TASK = "member.activity.task";

    @Queue(desc = "发送邀请活动模板消息通知", group = TopicGroup.MEMBER_GROUP)
    public static final String MEMBER_INVITE_ACTIVITY_TEMPLATE_NOTIFY = "member.invite.activity.template.notify";

    @Queue(desc = "智能付跑批", group = TopicGroup.TIMER_GROUP)
    public static final String AUTO_REMIT_TASK = "energy.auto.remit.task";
    @Queue(desc = "当日付跑批", group = TopicGroup.TIMER_GROUP)
    public static final String CURRENT_REMIT_TASK = "energy.current.remit.task";

    @Queue(desc = "平台付D+1跑批", group = TopicGroup.TIMER_GROUP)
    public static final String PLAT_REMIT_TASK_D1 = "energy.plat.d1.remit.task";

    @Queue(desc = "商户提现跑批", group = TopicGroup.TIMER_GROUP)
    public static final String WITHDRAW_REMIT_TASK = "energy.withdraw.remit.task";
    @Queue(desc = "通道管理", group = TopicGroup.TIMER_GROUP)
    public static final String CHANNEL_MANAGE_TASK = "energy.channel.manage.task";
    @Queue(desc = "班次结算处理", group = TopicGroup.REMIT_GROUP)
    public static final String SHIFT_REMIT_TASK = "energy.shift.remit.task";
    @Queue(desc = "智能付处理", group = TopicGroup.REMIT_GROUP)
    public static final String DO_AUTO_REMIT = "energy.do.auto.remit";
    @Queue(desc = "出款自动补单", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_REISSUE = "energy.auto.remit.reissue";
    @Queue(desc = "智能付出款处理", group = TopicGroup.REMIT_GROUP)
    public static final String DO_AUTO_REMIT_PAYMENT = "energy.do.auto.remit.payment";
    @Queue(desc = "订单批次更新处理", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_BATCH_UPDATE = "energy.auto.remit.batch.update";
    @Queue(desc = "智能付延迟出款处理", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_DELAY = "energy.remit.delay";
    @Queue(desc = "交班流程-智能付班次结算完成", group = TopicGroup.REMIT_GROUP)
    public static final String AUTO_REMIT_SHIFT_COMPLETE = "energy.refund.autoRemitShiftComplete";
    @Queue(desc = "交班流程-平台付班次结算完成", group = TopicGroup.REMIT_GROUP)
    public static final String PLAT_SHIFT_COMPLETE = "energy.refund.platRemitShiftComplete";
    @Queue(desc = "交班流程-当日付班次结算完成", group = TopicGroup.REMIT_GROUP)
    public static final String CURRENT_REMIT_SHIFT_COMPLETE = "energy.refund.currentRemitShiftComplete";
    @Queue(desc = "付款完成", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_COMPLETE = "VirtualTopic.remit.complete";

    //-------------------------------------- 清结算START ------------------------------------------
    @Queue(desc = "日结算发起", group = TopicGroup.REMIT_GROUP)
    public static final String DAILY_SETTLE_LAUNCH = "energy.settle.dailySettleLaunch";
    @Queue(desc = "日结算统计", group = TopicGroup.REMIT_GROUP)
    public static final String DAILY_SETTLE_DO_STATISTIC = "energy.settle.dailySettleStatistic";
    @Queue(desc = "交班结算统计", group = TopicGroup.REMIT_GROUP)
    public static final String SHIFT_SETTLE_DO_STATISTIC = "energy.settle.shiftSettleStatistic";
    @Queue(desc = "结算统计完成", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_STATISTIC_FINISH = "VirtualTopic.energy.settle.statisticFinish";
    @Queue(desc = "商户结算后打款分发", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_MCH_REMIT_DISTRIBUTE = "energy.settle.mchRemit";
    @Queue(desc = "结算打款处理", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_REMIT_PROCESS = "energy.settle.remitProcess";
    @Queue(desc = "请求通道打款", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_REMIT_CHANNEL = "energy.settle.remitChannel";
    @Queue(desc = "重新请求通道打款", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_REMIT_CHANNEL_RETRY = "energy.settle.remitChannelRetry";
    @Queue(desc = "通道打款完成通知", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_REMIT_CHANNEL_COMPLETE = "energy.settle.channelRemitComplete";
    @Queue(desc = "打款完成回调付款明细", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_COMPLETE_NOTIFY_SETTLE = "energy.settle.remitComplete.toSettleItem";
    @Queue(desc = "打款完成回调代理商分账明细", group = TopicGroup.REMIT_GROUP)
    public static final String REMIT_COMPLETE_NOTIFY_AGENT = "energy.settle.remitComplete.toAgentAlt";
    @Queue(desc = "付款明细出款完成", group = TopicGroup.REMIT_GROUP)
    public static final String SETTLE_ITEM_REMIT_COMPLETE = "VirtualTopic.energy.settle.settleItemComplete";
    //-------------------------------------- 清结算END ------------------------------------------

    // 会员系统
    @Queue(desc = "会员注册完毕", group = TopicGroup.MEMBER_GROUP)
    public final static String MEMBER_REGISTER_COMPLETE = "VirtualTopic.energy.member.register.complete";

    @Queue(desc = "油卡激活完毕", group = TopicGroup.OIL_CARD_GROUP)
    public final static String OIL_CARD_ACTIVE_COMPLETE = "VirtualTopic.energy.member.oilCardActive.complete";

    @Queue(desc = "商户产品开通通知", group = TopicGroup.PRODUCT)
    public static final String MCH_PRODUCT_OPEN_NOTIFY = "VirtualTopic.mch.product.open";

    @Queue(desc = "产品过期邮件通知", group = TopicGroup.PRODUCT)
    public final static String PRODUCT_EMAIL_NOTIFY = "energy.product.email.notify";

    @Queue(desc = "产品过期短信通知", group = TopicGroup.PRODUCT)
    public final static String PRODUCT_SMS_NOTIFY = "energy.product.sms.notify";

    @Queue(desc = "产品开通到期通知", group = TopicGroup.PRODUCT)
    public static final String TIME_PRODUCT_VALIDITY = "energy.product.validity.task";

    @Queue(desc = "产品到期30天汇总统计", group = TopicGroup.PRODUCT)
    public static final String TIME_PRODUCT_VALIDITY_STATISTICS = "energy.product.validity.statistics.task";
    /**
     * 产品过期禁用通知
     */
    @Queue(desc = "产品过期禁用通知", group = TopicGroup.PRODUCT)
    public final static String PRODUCT_STATUS_NOTIFY = "energy.product.status.notify";

    @Queue(desc = "班次重新统计通知定时任务", group = TopicGroup.SHIFT_GROUP)
    public final static String SHIFT_RECOUNT = "energy.shift.recount";

    @Queue(desc = "交班结束逻辑处理通知", group = TopicGroup.SHIFT_GROUP)
    public final static String SHIFT_END_HANDLE_NOTIFY = "energy.shift.end.handle.notify";

    @Queue(desc = "通道分账出款订单完成", group = TopicGroup.REMIT_GROUP)
    public final static String CHANNEL_ALT_REMIT_COMPLETE = "energy.channel.alt.remit.complete";

    @Queue(desc = "通道商户分账入网新增或者修改异步通知", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_CREATE_MODIFY = "energy.channel.alt.mch.create.modify";

    @Queue(desc = "通道商户分账入网初步受理成功", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_CREATE = "VirtualTopic.energy.channel.alt.mch.init";

    @Queue(desc = "分账方认证通过", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_AUTH_SUCCESS = "VirtualTopic.energy.channel.alt.mch.authSuccess";

    @Queue(desc = "分账方图片审核不通过", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_PICTURE_DISAPPROVE = "energy.channel.alt.mch.picture.disapprove";

    @Queue(desc = "分账方签约结果通知", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_SIGN = "energy.channel.alt.sign";

    @Queue(desc = "通道商户分账方入网完成通知", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_CREATE_COMPLETE = "energy.channel.alt.mch.create.complete";

    @Queue(desc = "通道商户分账方修改信息通知", group = TopicGroup.ALT)
    public final static String CHANNEL_ALT_MCH_MODIFY_AUTH = "energy.channel.alt.mch.modify.auth";

    @Queue(desc = "商户同步收银员支付密码异步通知", group = TopicGroup.MERCHANT)
    public final static String MCH_TRADE_PWD_CHANGE = "VirtualTopic.energy.mch.trade.pwd.change";

    @Queue(desc = "商户同步收银员登录密码异步通知", group = TopicGroup.MERCHANT)
    public final static String MCH_LOGIN_PWD_CHANGE = "VirtualTopic.energy.mch.login.pwd.change";

    @Queue(desc = "PDA收银员同步管理员登录密码异步通知", group = TopicGroup.MERCHANT)
    public final static String PDA_CASHIER_LOGIN_PWD_CHANGE = "VirtualTopic.energy.pda.login.pwd.change";

    @Queue(desc = "PDA收银员同步管理员支付密码异步通知", group = TopicGroup.MERCHANT)
    public final static String PDA_CASHIER_TRADE_PWD_CHANGE = "VirtualTopic.energy.pda.trade.pwd.change";

    @Queue(desc = "PDA收银员同步管理员交易登录密码异步通知", group = TopicGroup.MERCHANT)
    public final static String PDA_CASHIER_TRADE_LOGIN_PWD_CHANGE = "VirtualTopic.energy.pda.trade.login.pwd.change";

    @Queue(desc = "修改商户油站外景图重新生成收款二维码异步通知", group = TopicGroup.MERCHANT)
    public final static String MCH_STATION_OUTDOOR_SCENE_CHANGE = "energy.mch.paymentCode.image.change";

    @Queue(desc = "易加油产品列表定时查询", group = TopicGroup.TIMER_GROUP)
    public static final String EJY_OIL_STATION_TASK = "energy.ejy.oil.station.task";

    @Queue(desc = "易加油产品列表更新或新增", group = TopicGroup.EJIAYOU_GROUP)
    public static final String EJY_STATION_UPDATE_ADD_TASK = "energy.ejy.station.update.add.task";

    @Queue(desc = "检查易加油无用油站定时查询", group = TopicGroup.TIMER_GROUP)
    public static final String USELESS_OIL_STATION_TASK = "energy.useless.oil.station.task";

    @Queue(desc = "壳牌卡券状态变更通知", group = TopicGroup.QIAOPAI)
    public static final String QIAOPAI_COUPON_STATUS_CHANGE = "energy.qiaopai.coupon.status.change";

    @Queue(desc = "智享汇（旧）通道单笔发放完成通知", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String CHANNEL_SINGLE_GRANT_COMPLETE = "energy.channel.single.grant.complete.notify";
    @Queue(desc = "智享汇（新）通道单笔发放完成通知", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String ZHIXIANGHUI_CHANNEL_SINGLE_GRANT_COMPLETE = "energy.zhixianghui.channel.single.grant.complete.notify";
    @Queue(desc = "通道单笔发放完成通知(税筹)", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String SINGLE_GRANT_COMPLETE_TAX = "single.grant.channel.complete.tax";
    @Queue(desc = "通道单笔发放完成通知(税筹直签)", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String DIRECT_SIGN_SINGLE_GRANT_COMPLETE_TAX = "direct.sign.single.grant.channel.complete.tax";

    @Queue(desc = "通道单笔发放完成通知(积分)", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String SINGLE_GRANT_COMPLETE_POINTS = "single.grant.channel.complete.points";

    @Queue(desc = "车车网订单变更异步通知", group = TopicGroup.TRAFFIC_GROUP)
    public static final String CHE_CAR_ORDER_CHANGE_NOTIFY = "energy.checar.order.change.notify";
    @Queue(desc = "车友村积分兑换提现-补单", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String GRANT_REISSUE = "energy.grant.reissue";
    @Queue(desc = "支付订单语音播报记录删除", group = TopicGroup.TIMER_GROUP)
    public static final String EXPIRED_ORDER_TTS_DELETE_TASK = "energy.flowTradePayOrder.tts.delete.task";

    /**
     * 智享汇-资金商户提现业务
     **/
    @Queue(desc = "提现订单账户扣款后请求通道打款", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String FUND_MCH_ACCOUNT_DEBIT_AND_REMIT = "energy.fund.mch.accountDebitAndRemit";
    @Queue(desc = "资金商户提现完成通知", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String FUND_MCH_GRANT_COMPLETE = "VirtualTopic.energy.fund.mch.grant.complete";
    @Queue(desc = "直接转让审核通过", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String FUND_MCH_GRANT_IMPORT_AUDIT_PASS = "energy.fund.mch.grantImport.auditPass";
    @Queue(desc = "直接转让明细请求出款", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String FUND_MCH_GRANT_IMPORT_DETAIL_REQ_GRANT = "energy.fund.mch.grantImport.detailReqGrant";
    @Queue(desc = "平台商户入网成功", group = TopicGroup.MERCHANT)
    public final static String PLAT_MERCHANT_CREATE_COMPLETED = "VirtualTopic.energy.plat.merchant.create.completed";
    @Queue(desc = "资金方充值回调完成通知", group = TopicGroup.ZHI_XIANG_HUI)
    public static final String FUND_CHANNEL_RECHARGE_NOTIFY = "energy.fund.channel.recharge.notify";

    /**
     * 车险积分兑换服务
     */
    @Queue(desc = "积分兑换提现-补单", group = TopicGroup.TIMER_GROUP)
    public static final String DEAL_WITH_POINTS_EXPIRED_TIMER_WORK = "energy.points.expired.timer.work";
    @Queue(desc = "积分兑换油券补单", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_TO_OIL_COUPON_REISSUE_TASK = "energy.points.to.oilCoupon.reissue.task";
    @Queue(desc = "积分提现补单", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_TO_GRANT_REISSUE_TASK = "energy.points.to.grant.reissue.task";
    @Queue(desc = "积分提现打款中订单补单", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_TO_GRANT_REMITTING_REISSUE_TASK = "energy.points.to.grant.remitting.reissue.task";
    @Queue(desc = "积分服务每日统计", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_DAILY_STATISTIC_TASK = "energy.points.consume.statistic.task";
    @Queue(desc = "积分导入明细过期提醒", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_IMPORT_DETAIL_EXPIRE_WARN_TASK = "energy.points.importDetail.expireWarn.task";
    @Queue(desc = "积分导入每周统计", group = TopicGroup.TIMER_GROUP)
    public static final String POINTS_IMPORT_WEEK_STATISTIC_TASK = "energy.points.import.week.statistic.task";
    @Queue(desc = "积分上账导入完成", group = TopicGroup.CHEYOCUN_POINTS)
    public final static String POINTS_IMPORT_FINISH = "VirtualTopic.energy.points.import.finish";
    @Queue(desc = "积分消费发起", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String DEAL_WITH_POINTS_CONSUME = "energy.points.consume.launch";
    @Queue(desc = "积分账户注销", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String POINTS_ACCOUNT_CANCEL = "energy.points.account.cancel";
    @Queue(desc = "中石油油券兑换结果通知", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String CNPC_POINTS_TO_COUPON_RESULT_NOTIFY = "energy.points.to.cnpcOilCoupon.sendResultNotify";
    @Queue(desc = "欧飞直充结果通知", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String OUFEI_POINTS_TO_COUPON_RESULT_NOTIFY = "energy.points.to.ouFeiOilCoupon.sendResultNotify";
    @Queue(desc = "积分兑换电子券完成", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String POINTS_TO_ETICKET_COMPLETE = "VirtualTopic.points.to.eticket.complete";
    @Queue(desc = "积分消费完成", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String POINTS_CONSUME_COMPLETE = "VirtualTopic.energy.points.consume.complete";
    @Queue(desc = "中石油油券核销通知", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String CNPC_OIL_COUPON_USED_NOTIFY = "energy.points.cnpcOilCoupon.usedNotify";
    @Queue(desc = "车险退款订单支付完成", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String INSURANCE_REFUND_TRADE_CHANNEL_COMPLETE = "energy.points.insurance.refund.channel.complete";
    @Queue(desc = "积分快照数据生成", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String POINTS_TO_SNAPSHOT_DATA_GEN = "energy.points.insurance.snapshot.data.gen";
    @Queue(desc = "积分退款完成快照生成处理", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String POINTS_REFUND_COMPLETE_SNAPSHOT_DATA_GEN = "energy.points.insurance.refund.complete.snapshot.data.gen";

    //车险清单结算数据导入入库操作
    @Queue(desc = "车险清单结算数据导入入库操作", group = TopicGroup.CHEYOCUN_POINTS)
    public static final String INSURANCE_SETTLE_DATA_GEN = "energy.insurance.settle.data.gen";


    /**
     * 轻量化商户交易相关
     */
    @Queue(desc = "支付记录回调收单订单(轻量化)", group = TopicGroup.REFUND_GROUP)
    public static final String ALT_TRADE_PAY_RECORD_CALLBACK_PAY_ORDER = "altTrade.pay.callback.payOrder";
    @Queue(desc = "通道支付完成(轻量化)", group = TopicGroup.REFUND_GROUP)
    public static final String ALT_TRADE_PAY_CHANNEL_COMPLETE = "altTrade.pay.channelComplete";
    @Queue(desc = "通道退款完成(轻量化)", group = TopicGroup.REFUND_GROUP)
    public static final String ALT_TRADE_REFUND_CHANNEL_COMPLETE = "altTrade.refund.channelComplete";
    @Queue(desc = "退款记录回调退款订单(轻量化)", group = TopicGroup.REFUND_GROUP)
    public static final String ALT_TRADE_REFUND_CALLBACK_REFUND_ORDER = "altTrade.refund.callback.refundOrder";

    /**
     * 轻量化加油储值卡相关
     */
    @Queue(desc = "支付记录回调油卡充值订单(轻量化)", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_PAY_RECORD_CALLBACK_OILCARD_RECHARGE = "altTrade.pay.callback.oilCardRecharge";
    @Queue(desc = "支付记录回调油卡退款订单(轻量化)", group = TopicGroup.PAYMENT_GROUP)
    public static final String ALT_TRADE_PAY_RECORD_CALLBACK_OILCARD_REFUND = "altTrade.refund.callback.oilCardRecharge";
    @Queue(desc = "轻量化加油储值卡充值订单完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String FLOW_OILCARD_RECHARGE_COMPLETE = "VirtualTopic.flowOilCard.recharge.complete";
    @Queue(desc = "轻量化加油储值卡消费订单完成", group = TopicGroup.PAYMENT_GROUP)
    public static final String FLOW_OILCARD_CONSUME_COMPLETE = "VirtualTopic.flowOilCard.consume.complete";
    @Queue(desc = "轻量化加油储值卡退款订单完成", group = TopicGroup.REFUND_GROUP)
    public static final String FLOW_OILCARD_RECHARGE_REFUND_COMPLETE = "VirtualTopic.flowOilCard.recharge.refund.complete";

    /**
     * 轻量化会员营销相关
     */
    @Queue(desc = "会员营销定时处理过期优惠券", group = TopicGroup.MEMBER_GROUP)
    public static final String LIGHT_WEIGHT_MEMBER_MARKETING_COUPON_EXPIRE_TASK = "energy.lightWeight.member.marketing.coupon.expire.task";
    @Queue(desc = "会员营销定时处理过期活动", group = TopicGroup.MEMBER_GROUP)
    public static final String LIGHT_WEIGHT_MEMBER_MARKETING_ACTIVITY_EXPIRE_TASK = "energy.lightWeight.member.marketing.activity.expire.task";
    @Queue(desc = "轻量化会员注册成功", group = TopicGroup.MEMBER_GROUP)
    public static final String LIGHT_WEIGHT_MEMBER_REGISTER_COMPLETE = "energy.lightWeight.member.register.complete";

    /**
     * 税筹相关
     */
    @Queue(desc = "税筹单笔提现通知", group = TopicGroup.TAX_GROUP)
    public static final String TAX_SINGLE_GRANT_NOTIFY = "energy.tax.single.grant.notify";
    @Queue(desc = "税筹本地账务完成通知", group = TopicGroup.TAX_GROUP)
    public static final String TAX_ACCOUNT_COMPLETE_NOTIFY = "energy.tax.account.complete.notify";
    @Queue(desc = "税筹充值回调完成通知", group = TopicGroup.TAX_GROUP)
    public static final String TAX_CHANNEL_RECHARGE_NOTIFY = "energy.tax.channel.recharge.notify";
    @Queue(desc = "税筹提现订单完成", group = TopicGroup.TAX_GROUP)
    public static final String TAX_GRANT_COMPLETE = "VirtualTopic.tax.grant.complete";
    @Queue(desc = "税筹解决提现订单处理中状态跑批", group = TopicGroup.TAX_GROUP)
    public static final String TAX_GRANT_STATUS_HANDLE_TASK = "energy.tax.grant.status.handle.task";
    @Queue(desc = "税筹充值入账邮件提醒跑批任务", group = TopicGroup.TAX_GROUP)
    public static final String TAX_RECHARGE_EMAIL_WARN_TASK = "energy.tax.recharge.email.warn.task";

    /**
     * 税筹直签商户相关
     */
    @Queue(desc = "(直签)税筹发起单笔提现通知", group = TopicGroup.TAX_GROUP)
    public static final String TAX_DIRECT_SIGN_SINGLE_GRANT_NOTIFY = "energy.tax.direct.sign.single.grant.notify";
    @Queue(desc = "(直签)税筹充值回调完成通知", group = TopicGroup.TAX_GROUP)
    public static final String TAX_DIRECT_SIGN_CHANNEL_RECHARGE_NOTIFY = "energy.tax.direct.sign.channel.recharge.notify";
    @Queue(desc = "(直签)税筹提现订单完成", group = TopicGroup.TAX_GROUP)
    public static final String TAX_DIRECT_SIGN_GRANT_COMPLETE = "VirtualTopic.tax.direct.sign.grant.complete";
    @Queue(desc = "(直签)税筹解决提现订单处理中状态跑批", group = TopicGroup.TAX_GROUP)
    public static final String TAX_DIRECT_SIGN_GRANT_STATUS_HANDLE_TASK = "energy.tax.direct.sign.grant.status.handle.task";
    @Queue(desc = "(直签)税筹充值入账邮件提醒跑批任务", group = TopicGroup.TAX_GROUP)
    public static final String TAX_DIRECT_SIGN_RECHARGE_EMAIL_WARN_TASK = "energy.tax.direct.sign.recharge.email.warn.task";

    /**
     * 电子协议相关
     */
    @Queue(desc = "电子协议发起签署", group = TopicGroup.MERCHANT)
    public static final String ESIGN_AGREEMENT_LAUNCH = "energy.esign.agreement.launch";

    @Queue(desc = "电子协议通知回调", group = TopicGroup.MERCHANT)
    public static final String ESIGN_AGREEMENT_NOTIFY = "energy.esign.agreement.notify";

    @Queue(desc = "电子协议通知回调V3", group = TopicGroup.MERCHANT)
    public static final String ESIGN_AGREEMENT_NOTIFY_V3 = "energy.esign.agreement.notify.v3";


    /**
     * key为属性值，value为该属性上@Queue注解的描述内容
     *
     * @return
     */
    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();

        Field[] fields = TopicDest.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null) {
                continue;
            }

            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(TopicDest.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            map.put(name, desc);
        }
        return map;
    }

    /**
     * 二维map，第一个key为分组名，第二个key为属性值，value为该属性上@Queue注解的描述内容
     *
     * @return
     */
    public static Map<String, Map<String, String>> toGroupMap() {
        Map<String, Map<String, String>> outMap = new LinkedHashMap<>();

        Field[] fields = TopicDest.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null) {
                continue;
            }

            String group = queue.group();
            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(TopicDest.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            if (!outMap.containsKey(group)) {
                outMap.put(group, new LinkedHashMap<>());
            }
            outMap.get(group).put(name, desc);
        }
        return outMap;
    }

    public static Map<String, String> getMap(String group) {
        if (group == null) {
            return new LinkedHashMap<>();
        }

        Map<String, String> map = new LinkedHashMap<>();
        Field[] fields = TopicDest.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null || !group.equals(queue.group())) {
                continue;
            }

            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(TopicDest.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            map.put(name, desc);
        }
        return map;
    }

    public static String getGroup(String topic) {
        if (topic == null) {
            return "";
        }
        Field[] fields = TopicDest.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null) {
                continue;
            }
            try {
                field.setAccessible(true);
                String name = String.valueOf(field.get(TopicDest.class));
                if (!topic.equals(name)) {
                    continue;
                } else {
                    return queue.group();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return "";
    }
}
