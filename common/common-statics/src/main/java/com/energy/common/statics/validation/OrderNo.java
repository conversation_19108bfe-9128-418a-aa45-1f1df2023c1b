package com.energy.common.statics.validation;

import java.lang.annotation.*;

/**
 * 被注解参数须为 String 类型，校验规则为：不能为null、去掉空格之后长度介于 min ~ max 之间、只能由字母/数字/下划线/中划线组成
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = OrderNoValidator.class)
public @interface OrderNo {
    String message() default "订单号格式错误";

    /**
     * 在哪些场景下会校验，比如：add、edit、delete，不填则表示适用所有场景
     */
    String[] scenes() default {};

    /**
     * 最小长度
     */
    int min() default 3;

    /**
     * 最大长度
     */
    int max() default 64;
}
