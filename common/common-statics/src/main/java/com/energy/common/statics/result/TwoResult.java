package com.energy.common.statics.result;

import java.io.Serializable;

/**
 * 用在一个方法需要返回2个值的情况
 * @param <L>
 * @param <R>
 */
public class TwoResult<L,R> implements Serializable {
    private L left;
    private R right;

    public TwoResult(){}

    public TwoResult(L left, R right){
        this.left = left;
        this.right = right;
    }

    public L getLeft() {
        return left;
    }

    public void setLeft(L left) {
        this.left = left;
    }

    public R getRight() {
        return right;
    }

    public void setRight(R right) {
        this.right = right;
    }
}
