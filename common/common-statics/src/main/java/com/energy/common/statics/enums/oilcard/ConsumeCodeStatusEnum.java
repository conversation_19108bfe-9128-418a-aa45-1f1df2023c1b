package com.energy.common.statics.enums.oilcard;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ConsumeStatusEnum 消费码查询状态
 *
 * <AUTHOR>
 * @date 2020/4/27
 */
public enum ConsumeCodeStatusEnum {

    USED("已使用", 100),
    NOTUSED("未使用", 101),
    EXPIRED("已过期", 102),
    NOTEXIST("不存在", 103);
    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String desc;

    ConsumeCodeStatusEnum(String desc, int value) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static ConsumeCodeStatusEnum getEnum(int value) {
        ConsumeCodeStatusEnum resultEnum = null;
        for (ConsumeCodeStatusEnum typeEnum : ConsumeCodeStatusEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<UrlBizTypeEnum> toList() {
        return Arrays.asList(UrlBizTypeEnum.values());
    }

    public static Map<Integer, String> toMap() {
        Map<Integer, String> enumMap = new HashMap<>();
        for (ConsumeCodeStatusEnum typeEnum : ConsumeCodeStatusEnum.values()) {
            enumMap.put(typeEnum.value, typeEnum.desc);
        }
        return enumMap;
    }
}
