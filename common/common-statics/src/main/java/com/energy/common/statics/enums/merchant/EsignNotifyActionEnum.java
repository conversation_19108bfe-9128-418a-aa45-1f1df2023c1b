package com.energy.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * e签宝 回调Action事件列表
 */
public enum EsignNotifyActionEnum {

    SIGN_FLOW_UPDATE("SIGN_FLOW_UPDATE", "签署人签署完成"),
    SIGN_FLOW_FINISH("SIGN_FLOW_FINISH", "流程结束"),
    SIGN_DOC_EXPIRE_REMIND("SIGN_DOC_EXPIRE_REMIND", "流程文件过期前提醒"),
    SIGN_DOC_EXPIRE("SIGN_DOC_EXPIRE", "流程文件过期"),

    ;

    /**
     * 枚举值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    EsignNotifyActionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static EsignNotifyActionEnum getEnum(String value) {
        return Arrays.stream(values()).filter(p -> p.value.equals(value)).findFirst().orElse(null);
    }
}
