package com.energy.common.statics.constants.mqdest;

import com.energy.common.statics.annotations.Queue;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 虚拟队列 (VirtualTopic) 的消费者
 */
public class VTopicConsume {
    //API收单start
    @Deprecated
    @Queue(desc = "收单订单支付完成商户通知", vtopic = TopicDest.PAYMENT_COMPLETE)
    public final static String PAYMENT_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.PAYMENT_COMPLETE;
    @Deprecated
    @Queue(desc = "收单订单支付完成计算手续费", vtopic = TopicDest.PAYMENT_COMPLETE)
    public static final String PAYMENT_COMPLETE_CALCULATE_FEE = "Consumer.CalculateFee." + TopicDest.PAYMENT_COMPLETE;
    @Deprecated
    @Queue(desc = "收单订单支付完成订单信息推送", vtopic = TopicDest.PAYMENT_COMPLETE)
    public static final String PAYMENT_COMPLETE_ORDER_INFO_PUSH = "Consumer.OrderInfoPush." + TopicDest.PAYMENT_COMPLETE;

    @Queue(desc = "收单订单支付完成商户通知", vtopic = TopicDest.PAY_ORDER_COMPLETE)
    public final static String PAY_ORDER_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.PAY_ORDER_COMPLETE;
    @Queue(desc = "收单订单支付完成订单信息推送", vtopic = TopicDest.PAY_ORDER_COMPLETE)
    public static final String PAY_ORDER_COMPLETE_ORDER_INFO_PUSH = "Consumer.OrderInfoPush." + TopicDest.PAY_ORDER_COMPLETE;
    //API收单end

    //收款系统start
    @Queue(desc = "收款订单支付完成商户通知", vtopic = TopicDest.TRADE_COMPLETE)
    public final static String TRADE_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成计算手续费", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_CALCULATE_FEE = "Consumer.CalculateFee." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成打印小票", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成权益处理", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_PRIORITY = "Consumer.Priority." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成积分变动", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_CREDIT_ALTER = "Consumer.CreditAlter." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成补充订单会员信息", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_FILL_MEMBER_INFO = "Consumer.FillMemberInfo." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成活动发放商户权益", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_ACTIVITY_GIVE_OUT_PRIORITY = "Consumer.ActivityGiveOutPriority." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成发放邀请人拉新奖励", vtopic = TopicDest.TRADE_COMPLETE)
    public static final String TRADE_COMPLETE_GIVE_INVITER_ACTIVITY_AWARD = "Consumer.GiveInviterActivityAward." + TopicDest.TRADE_COMPLETE;
    @Queue(desc = "油卡消费订单完成活动发放商户权益", vtopic = TopicDest.CONSUME_COMPLETE)
    public static final String CONSUME_COMPLETE_ACTIVITY_GIVE_OUT_PRIORITY = "Consumer.ActivityGiveOutPriority." + TopicDest.CONSUME_COMPLETE;
    @Queue(desc = "油卡消费订单完成打印小票", vtopic = TopicDest.CONSUME_COMPLETE)
    public static final String CONSUME_COMPLETE_PRINT = "Consumer.print." + TopicDest.CONSUME_COMPLETE;
    @Queue(desc = "油卡消费订单完成权益处理", vtopic = TopicDest.CONSUME_COMPLETE)
    public static final String CONSUME_COMPLETE_PRIORITY_USE = "Consumer.priorityUse." + TopicDest.CONSUME_COMPLETE;
    @Queue(desc = "油卡消费订单完成发放邀请人拉新奖励", vtopic = TopicDest.CONSUME_COMPLETE)
    public static final String CONSUME_COMPLETE_GIVE_INVITER_ACTIVITY_AWARD = "Consumer.GiveInviterActivityAward." + TopicDest.CONSUME_COMPLETE;
    @Queue(desc = "油卡激活完成发放拉新邀请奖励", vtopic = TopicDest.OIL_CARD_ACTIVE_COMPLETE)
    public static final String OIL_CARD_ACTIVE_COMPLETE_GIVE_INVITE_ACTIVITY_AWARD = "Consumer.GiveInviteActivityAward." + TopicDest.OIL_CARD_ACTIVE_COMPLETE;
    // 分账
    @Queue(desc = "分账收款订单支付完成打印小票", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成流量管家发放分销佣金", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_GIVE_ALT_AWARD = "Consumer.GiveAltAward." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成流量管家权益核销", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DEAL_PRIORITY_USE = "Consumer.dealPriority." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成通知第三方平台", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_NOTIFY_THIRD_PLAT = "Consumer.Notify." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成处理会员权益", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DO_PRIORITY = "Consumer.Priority." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成处理会员消费统计信息", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DO_MEMBER_CONSUME_INFO = "Consumer.memberConsumeInfo." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成处理会员积分处理", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DO_MEMBER_POINTS = "Consumer.memberPointsAdd." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成语音播报转换", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DO_TTS = "Consumer.tts." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成油厂订单回调通知", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_NOTIFY_OIL_ENGINE = "Consumer.Notify.oilEngine." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成处理优惠券发放", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DISTRIBUTE_COUPON = "Consumer.distributeMarketingCoupon." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "收款订单支付完成处理优惠劵状态", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_DO_MEMBER_MARKETING_COUPON = "Consumer.memberMarketingCoupon." + TopicDest.ALT_TRADE_COMPLETE;
    @Queue(desc = "分账收款订单支付完成通知商户回调", vtopic = TopicDest.ALT_TRADE_COMPLETE)
    public static final String ALT_TRADE_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.ALT_TRADE_COMPLETE;

    // 车务订单
    @Queue(desc = "车务订单-收款订单支付完成通知第三方平台", vtopic = TopicDest.TRAFFIC_TRADE_COMPLETE)
    public static final String TRAFFIC_TRADE_COMPLETE_NOTIFY_CCW = "Consumer.NotifyCcw." + TopicDest.TRAFFIC_TRADE_COMPLETE;
    @Queue(desc = "车务订单-支付完成处理权益核销", vtopic = TopicDest.TRAFFIC_TRADE_COMPLETE)
    public static final String TRAFFIC_TRADE_COMPLETE_DO_WITH_PRIORITY = "Consumer.Priority." + TopicDest.TRAFFIC_TRADE_COMPLETE;
    @Queue(desc = "车务订单-退款完成处理权益核销", vtopic = TopicDest.TRAFFIC_REFUND_COMPLETE)
    public static final String TRAFFIC_REFUND_COMPLETE_DO_PRIORITY = "Consumer.GivePriorityBack." + TopicDest.TRAFFIC_REFUND_COMPLETE;
    //收款系统end

    //充值start
    @Queue(desc = "充值订单支付完成商户通知", vtopic = TopicDest.RECHARGE_COMPLETE)
    public static final String RECHARGE_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.RECHARGE_COMPLETE;
    @Queue(desc = "充值订单支付完成计算手续费", vtopic = TopicDest.RECHARGE_COMPLETE)
    public static final String RECHARGE_COMPLETE_CALCULATE_FEE = "Consumer.CalculateFee." + TopicDest.RECHARGE_COMPLETE;
    @Queue(desc = "充值订单支付完成积分变动", vtopic = TopicDest.RECHARGE_COMPLETE)
    public static final String RECHARGE_COMPLETE_CREDIT_ALTER = "Consumer.CreditAlter." + TopicDest.RECHARGE_COMPLETE;
    //充值end

    //退款系统start
    @Deprecated
    @Queue(desc = "退款订单完成商户通知", vtopic = TopicDest.REFUND_COMPLETE)
    public static final String REFUND_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.REFUND_COMPLETE;
    @Deprecated
    @Queue(desc = "退款订单完成更新订单退款信息", vtopic = TopicDest.REFUND_COMPLETE)
    public static final String REFUND_COMPLETE_UPDATE_ORDER_REFUND_INFO = "Consumer.UpdateOrderRefundInfo." + TopicDest.REFUND_COMPLETE;
    @Deprecated
    @Queue(desc = "退款订单完成打印小票", vtopic = TopicDest.REFUND_COMPLETE)
    public static final String REFUND_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.REFUND_COMPLETE;
    @Deprecated
    @Queue(desc = "退款订单完成代理商订单同步", vtopic = TopicDest.REFUND_COMPLETE)
    public static final String REFUND_COMPLETE_AGENT_ORDER_SYNC = "Consumer.AgentOrderSync." + TopicDest.REFUND_COMPLETE;
    @Deprecated
    @Queue(desc = "本地退款订单完成更新订单退款信息", vtopic = TopicDest.NATIVE_REFUND_COMPLETE)
    public static final String NATIVE_REFUND_COMPLETE_UPDATE_ORDER_REFUND_INFO = "Consumer.UpdateOrderRefundInfo." + TopicDest.NATIVE_REFUND_COMPLETE;

    @Queue(desc = "退款订单完成商户通知", vtopic = TopicDest.REFUND_ORDER_COMPLETE)
    public static final String REFUND_ORDER_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.REFUND_ORDER_COMPLETE;
    @Queue(desc = "退款订单完成代理商订单同步", vtopic = TopicDest.REFUND_ORDER_COMPLETE)
    public static final String REFUND_ORDER_COMPLETE_AGENT_ORDER_SYNC = "Consumer.AgentOrderSync." + TopicDest.REFUND_ORDER_COMPLETE;
    //退款系统end

    //轻量化退款系统star
    @Queue(desc = "退款订单完成更新订单退款信息", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_UPDATE_ORDER_REFUND_INFO = "Consumer.UpdateOrderRefundInfo." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成打印小票", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成更新流量管家分销团队退款信息", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_UPDATE_ALT_TEAM_REFUND_INFO = "Consumer.UpdateAltTeamRefundInfo." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "处理退款订单完成加油金回退", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_DEAL_PRIORITY = "Consumer.dealPriority." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成处理权益", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_DO_PRIORITY = "Consumer.Priority." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成-处理积分回退", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_DEAL_WITH_POINTS = "Consumer.DealWithPoints." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成营销优惠券撤销", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String REFUND_COMPLETE_ACTIVITY_PRIORITY_RECEIVE_ROLL_BACK = "Consumer.RollBackCoupon." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    @Queue(desc = "退款订单完成-处理商户通知", vtopic = TopicDest.LIGHT_MCH_REFUND_COMPLETE)
    public static final String LIGHT_MCH_REFUND_COMPLETE_MERCHANT_NOTIFY = "Consumer.MerchantNotify." + TopicDest.LIGHT_MCH_REFUND_COMPLETE;
    //轻量化退款系统end

    // 会员系统start
    @Queue(desc = "会员注册完成发放权益", vtopic = TopicDest.MEMBER_REGISTER_COMPLETE)
    public static final String MEMBER_REGISTER_COMPLETE_ACTIVITY_GIVE_PRIORITY = "Consumer.ActivityGivePriority." + TopicDest.MEMBER_REGISTER_COMPLETE;
    @Queue(desc = "会员注册完成发放拉新奖励", vtopic = TopicDest.MEMBER_REGISTER_COMPLETE)
    public static final String MEMBER_REGISTER_COMPLETE_GIVE_INVITE_ACTIVITY_AWARD = "Consumer.GiveInviteActivityAward." + TopicDest.MEMBER_REGISTER_COMPLETE;
    // 会员系统end

    // 付款系统start
    @Queue(desc = "付款完成智能付处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_AUTO_REMIT = "Consumer.AutoRemit." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成当日付处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_CURRENT_REMIT = "Consumer.CurrentRemit." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成商户提现处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_WITHDRAW_REMIT = "Consumer.WithdrawRemit." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成流量管家会员提现处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_MEMBER_WITHDRAW_REMIT = "Consumer.MemberWithdrawRemit." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成资金方提现业务层处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_FUND_MCH_GRANT_REMIT = "Consumer.FundMchGrantOrder." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成手续费分账订单处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_FEE_ALT_REMIT = "Consumer.feeAltRemit." + TopicDest.REMIT_COMPLETE;
    @Queue(desc = "付款完成平台付处理", vtopic = TopicDest.REMIT_COMPLETE)
    public static final String REMIT_COMPLETE_PLAT_REMIT = "Consumer.PlatRemit." + TopicDest.REMIT_COMPLETE;

    @Queue(desc = "商户结算后交班小票推送", vtopic = TopicDest.SETTLE_STATISTIC_FINISH)
    public static final String SHIFT_SETTLE_SHIFT_RECEIPT = "Consumer.pushReceipt." + TopicDest.SETTLE_STATISTIC_FINISH;
    @Queue(desc = "商户结算后d1/t1结算产品直接出款", vtopic = TopicDest.SETTLE_STATISTIC_FINISH)
    public static final String SETTLE_MCH_D1_OR_T1_DIRECT_DISTRIBUTE = "Consumer.mchD1OrT1DirectDistribute." + TopicDest.SETTLE_STATISTIC_FINISH;
    @Queue(desc = "商户结算后企业钱包入账", vtopic = TopicDest.SETTLE_STATISTIC_FINISH)
    public static final String SETTLE_MCH_WALLET_ENTRY = "Consumer.mchWalletEntry." + TopicDest.SETTLE_STATISTIC_FINISH;
    @Queue(desc = "商户结算后代理商结算打款(加油村1.0)", vtopic = TopicDest.SETTLE_STATISTIC_FINISH)
    public static final String SETTLE_AGENT_REMIT_DISTRIBUTE = "Consumer.agentRemit." + TopicDest.SETTLE_STATISTIC_FINISH;
    @Queue(desc = "商户结算后创建代理商分润分账记录(加油村2.0)", vtopic = TopicDest.SETTLE_STATISTIC_FINISH)
    public static final String SETTLE_AGENT_ALT_RECORD = "Consumer.agentAltRecord." + TopicDest.SETTLE_STATISTIC_FINISH;
    @Queue(desc = "付款明细完成处理付款批次", vtopic = TopicDest.SETTLE_ITEM_REMIT_COMPLETE)
    public static final String SETTLE_ITEM_COMPLETE_BATCH_PROCESS = "Consumer.processSettleBatch." + TopicDest.SETTLE_ITEM_REMIT_COMPLETE;
    @Queue(desc = "付款明细完成处理转账记录", vtopic = TopicDest.SETTLE_ITEM_REMIT_COMPLETE)
    public static final String SETTLE_ITEM_COMPLETE_TRANSFER_PROCESS = "Consumer.processTransfer." + TopicDest.SETTLE_ITEM_REMIT_COMPLETE;
    @Queue(desc = "付款明细完成处理钱包账户", vtopic = TopicDest.SETTLE_ITEM_REMIT_COMPLETE)
    public static final String SETTLE_ITEM_COMPLETE_WALLET_PROCESS = "Consumer.processWallet." + TopicDest.SETTLE_ITEM_REMIT_COMPLETE;
    // 付款系统end

    // 分账商户入网
    @Queue(desc = "通道商户分账入网受理成功后初始化商户信息", vtopic = TopicDest.CHANNEL_ALT_MCH_CREATE)
    public static final String INIT_ALT_MCH_MERCHANT_INFO = "Consumer.AltCreate." + TopicDest.CHANNEL_ALT_MCH_CREATE;
    @Queue(desc = "通道商户分账入网受理成功后初始化通道商户配置", vtopic = TopicDest.CHANNEL_ALT_MCH_CREATE)
    public static final String INIT_ALT_MCH_CHANNEL_CONFIG = "Consumer.ChannelConfig." + TopicDest.CHANNEL_ALT_MCH_CREATE;
    @Queue(desc = "通道商户分账入网受理成功后初始化油品信息", vtopic = TopicDest.CHANNEL_ALT_MCH_CREATE)
    public static final String INIT_ALT_MCH_OIL_INFO = "Consumer.InitOilInfo." + TopicDest.CHANNEL_ALT_MCH_CREATE;
    @Queue(desc = "通道商户分账入网受理成功后初始化积分配置", vtopic = TopicDest.CHANNEL_ALT_MCH_CREATE)
    public static final String INIT_ALT_MCH_POINTS_INTEREST_CONFIG = "Consumer.InitInterestConfig." + TopicDest.CHANNEL_ALT_MCH_CREATE;
    @Queue(desc = "通道商户分账入网受理成功后初始化产品开通", vtopic = TopicDest.CHANNEL_ALT_MCH_CREATE)
    public static final String INIT_ALT_MCH_PRODUCT_OPEN = "Consumer.OpenProduct." + TopicDest.CHANNEL_ALT_MCH_CREATE;
    @Queue(desc = "分账商户入网认证通过后初始化收款人", vtopic = TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS)
    public static final String INIT_RECEIVER_AFTER_CHANNEL_ALT_MCH_CREATE = "Consumer.WithdrawReceiver." + TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS;
    @Queue(desc = "分账商户入网认证通过后初始化油卡储值账户", vtopic = TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS)
    public static final String INIT_ALT_MCH_OIL_CARD_ACCOUNT = "Consumer.createOilCardAccount." + TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS;
    @Queue(desc = "分账商户入网认证通过后初始化平台付提现默认公户", vtopic = TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS)
    public static final String INIT_ALT_MCH_PLAT_RECEIVER = "Consumer.createPlatReceiver." + TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS;
    @Queue(desc = "分账商户入网认证通过后初始化企业钱包账户", vtopic = TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS)
    public static final String INIT_ALT_MCH_WALLET_ACCOUNT = "Consumer.createWalletAccount." + TopicDest.CHANNEL_ALT_MCH_AUTH_SUCCESS;

    //密码修改后数据同步
    @Queue(desc = "商户修改支付密码后，同步收银员支付密码信息", vtopic = TopicDest.MCH_TRADE_PWD_CHANGE)
    public static final String INIT_MCH_TRADE_PWD_CHANGE = "Consumer.Mch." + TopicDest.MCH_TRADE_PWD_CHANGE;
    @Queue(desc = "商户修改登录密码后，同步收银员登录密码信息", vtopic = TopicDest.MCH_LOGIN_PWD_CHANGE)
    public static final String INIT_MCH_LOGIN_PWD_CHANGE = "Consumer.Mch." + TopicDest.MCH_LOGIN_PWD_CHANGE;
    @Queue(desc = "PDA收银员修改支付密码，同步商户管理员支付密码信息", vtopic = TopicDest.PDA_CASHIER_TRADE_PWD_CHANGE)
    public static final String INIT_PDA_CASHIER_TRADE_PWD_CHANGE = "Consumer.pda." + TopicDest.PDA_CASHIER_TRADE_PWD_CHANGE;
    @Queue(desc = "PDA收银员修改登录密码，同步商户管理员登录密码信息", vtopic = TopicDest.PDA_CASHIER_LOGIN_PWD_CHANGE)
    public static final String INIT_PDA_CASHIER_LOGIN_PWD_CHANGE = "Consumer.pda." + TopicDest.PDA_CASHIER_LOGIN_PWD_CHANGE;
    @Queue(desc = "PDA收银员修改交易、登录密码，同步商户管理员交易、登录密码信息", vtopic = TopicDest.PDA_CASHIER_TRADE_LOGIN_PWD_CHANGE)
    public static final String INIT_PDA_CASHIER_TRADE_LOGIN_PWD_CHANGE = "Consumer.pda." + TopicDest.PDA_CASHIER_TRADE_LOGIN_PWD_CHANGE;

    // 资金方商户积分提现

    @Queue(desc = "积分上账完成短信通知车主", vtopic = TopicDest.POINTS_IMPORT_FINISH)
    public static final String POINTS_IMPORT_COMPLETE_SMS_NOTIFY = "Consumer.smsNotify." + TopicDest.POINTS_IMPORT_FINISH;
    @Queue(desc = "积分上账完成保司上账账户扣款(限额账户、积分账户等)", vtopic = TopicDest.POINTS_IMPORT_FINISH)
    public static final String POINTS_IMPORT_COMPLETE_IMPORT_ACCOUNT_DEBIT = "Consumer.importAccountDebit." + TopicDest.POINTS_IMPORT_FINISH;
    @Queue(desc = "积分上账完成生成积分快照数据", vtopic = TopicDest.POINTS_IMPORT_FINISH)
    public static final String POINTS_IMPORT_COMPLETE_GEN_SNAPSHOT = "Consumer.genPointsSnapshot." + TopicDest.POINTS_IMPORT_FINISH;
    @Queue(desc = "资金方提现完成商户通知处理", vtopic = TopicDest.FUND_MCH_GRANT_COMPLETE)
    public static final String FUND_MCH_GRANT_COMPLETE_MCH_NOTIFY = "Consumer.mchNotify." + TopicDest.FUND_MCH_GRANT_COMPLETE;
    @Queue(desc = "提现完成积分兑换记录处理", vtopic = TopicDest.FUND_MCH_GRANT_COMPLETE)
    public static final String FUND_MCH_GRANT_COMPLETE_POINTS_CONSUME = "Consumer.pointsConsume." + TopicDest.FUND_MCH_GRANT_COMPLETE;
    @Queue(desc = "提现完成积分直接转让处理", vtopic = TopicDest.FUND_MCH_GRANT_COMPLETE)
    public static final String FUND_MCH_GRANT_COMPLETE_GRANT_IMPORT = "Consumer.grantImport." + TopicDest.FUND_MCH_GRANT_COMPLETE;
    @Queue(desc = "积分兑换完成-证据链订单处理", vtopic = TopicDest.FUND_MCH_GRANT_COMPLETE)
    public static final String FUND_MCH_GRANT_COMPLETE_HANDLE_EVIDENCE_CHAIN_ORDER = "Consumer.handleEvidenceChainOrder." + TopicDest.FUND_MCH_GRANT_COMPLETE;
    @Queue(desc = "积分兑换完成资金方账户处理", vtopic = TopicDest.FUND_MCH_GRANT_COMPLETE)
    public static final String FUND_MCH_GRANT_COMPLETE_FUND_ACCOUNT_PROCESS = "Consumer.fundAccountProcess." + TopicDest.FUND_MCH_GRANT_COMPLETE;
    @Queue(desc = "积分兑换完成-处理失败回退积分", vtopic = TopicDest.POINTS_CONSUME_COMPLETE)
    public static final String POINTS_CONSUME_FAILED_ROLLBACK_POINTS = "Consumer.rollbackPoints." + TopicDest.POINTS_CONSUME_COMPLETE;
    @Queue(desc = "积分兑换完成-处理短信发送", vtopic = TopicDest.POINTS_CONSUME_COMPLETE)
    public static final String POINTS_CONSUME_COMPLETE_SEND_MSG = "Consumer.sendMessage." + TopicDest.POINTS_CONSUME_COMPLETE;
    @Queue(desc = "积分兑换完成-导入明细积分分配计算", vtopic = TopicDest.POINTS_CONSUME_COMPLETE)
    public static final String POINTS_CONSUME_COMPLETE_POINTS_USE_ALLOCATE = "Consumer.pointsUsedAllocate." + TopicDest.POINTS_CONSUME_COMPLETE;
    @Queue(desc = "积分兑换电子券完成-兑换记录处理", vtopic = TopicDest.POINTS_TO_ETICKET_COMPLETE)
    public static final String POINTS_TO_ETICKET_COMPLETE_CONSUME_RECORD = "Consumer.pointsConsumeRecord." + TopicDest.POINTS_TO_ETICKET_COMPLETE;
    @Queue(desc = "积分兑换电子券完成-api结果处理", vtopic = TopicDest.POINTS_TO_ETICKET_COMPLETE)
    public static final String POINTS_TO_ETICKET_COMPLETE_API = "Consumer.apiSendResult." + TopicDest.POINTS_TO_ETICKET_COMPLETE;

    //sass商户创建成功初始化
    @Queue(desc = "平台商户创建完成-结算账号初始化", vtopic = TopicDest.PLAT_MERCHANT_CREATE_COMPLETED)
    public static final String PLAT_MERCHANT_CREATE_COMPLETED_RECEIVER_INIT = "Consumer.receiverInit." + TopicDest.PLAT_MERCHANT_CREATE_COMPLETED;
    // 产品开通和初始化
    @Queue(desc = "平台商户创建完成-产品开通", vtopic = TopicDest.PLAT_MERCHANT_CREATE_COMPLETED)
    public static final String PLAT_MERCHANT_CREATE_COMPLETED_PRODUCT_OPEN = "Consumer.productOpen." + TopicDest.PLAT_MERCHANT_CREATE_COMPLETED;
    //加油村sass入网完成
    @Queue(desc = "平台商户创建完成-商户后台初始化", vtopic = TopicDest.PLAT_MERCHANT_CREATE_COMPLETED)
    public static final String PLAT_MERCHANT_CREATE_COMPLETED_INIT_MERCHANT_INFO = "Consumer.initMch." + TopicDest.PLAT_MERCHANT_CREATE_COMPLETED;
    @Queue(desc = "平台商户创建完成-企业钱包账户初始化", vtopic = TopicDest.PLAT_MERCHANT_CREATE_COMPLETED)
    public static final String PLAT_MERCHANT_CREATE_COMPLETED_WALLET_INIT = "Consumer.walletAccountInit." + TopicDest.PLAT_MERCHANT_CREATE_COMPLETED;


    @Queue(desc = "基础会员产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String BASE_MEMBER_INITIAL = "Consumer.baseMemberInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;
    @Queue(desc = "出款产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String REMIT_PRODUCT_INITIAL = "Consumer.remitInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;
    @Queue(desc = "油卡产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String OIL_CARD_PRODUCT_INITIAL = "Consumer.oilCardInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;
    @Queue(desc = "轻量化油卡产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String OIL_CARD_LITE_PRODUCT_INITIAL = "Consumer.oilCardLiteInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;
    @Queue(desc = "税筹提现产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String TAX_GRANT_PRODUCT_INITIAL = "Consumer.taxGrantInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;
    @Queue(desc = "加油村1.0/2.0交班产品初始化", vtopic = TopicDest.MCH_PRODUCT_OPEN_NOTIFY)
    public static final String SHIFT_PRODUCT_INITIAL = "Consumer.shiftInit." + TopicDest.MCH_PRODUCT_OPEN_NOTIFY;

    /**
     * 轻量化会员加油储值卡
     */
    @Queue(desc = "轻量化会员加油充值订单支付完成打印小票", vtopic = TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE)
    public static final String FLOW_OILCARD_RECHARGE_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE;
    @Queue(desc = "轻量化会员加油充值订单支付完成语音播报", vtopic = TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE)
    public static final String FLOW_OILCARD_RECHARGE_COMPLETE_DO_TTS = "Consumer.tts." + TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE;
    @Queue(desc = "轻量化会员加油充值订单支付完成账务处理", vtopic = TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE)
    public static final String FLOW_OILCARD_RECHARGE_COMPLETE_ACCOUNT = "Consumer.flowOilCardAccount." + TopicDest.FLOW_OILCARD_RECHARGE_COMPLETE;
    @Queue(desc = "轻量化会员加油储值卡消费订单完成打印小票", vtopic = TopicDest.FLOW_OILCARD_CONSUME_COMPLETE)
    public static final String FLOW_OILCARD_CONSUME_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.FLOW_OILCARD_CONSUME_COMPLETE;
    @Queue(desc = "轻量化会员加油储值卡消费订单完成语音播报", vtopic = TopicDest.FLOW_OILCARD_CONSUME_COMPLETE)
    public static final String FLOW_OILCARD_CONSUME_COMPLETE_DO_TTS = "Consumer.tts." + TopicDest.FLOW_OILCARD_CONSUME_COMPLETE;
    @Queue(desc = "轻量化会员加油储值卡充值订单退款完成账务处理", vtopic = TopicDest.FLOW_OILCARD_RECHARGE_REFUND_COMPLETE)
    public static final String FLOW_OILCARD_RECHARGE_REFUND_COMPLETE_ACCOUNT = "Consumer.flowOilCardAccount." + TopicDest.FLOW_OILCARD_RECHARGE_REFUND_COMPLETE;
    @Queue(desc = "轻量化会员加油储值卡充值订单退款完成小票打印", vtopic = TopicDest.FLOW_OILCARD_RECHARGE_REFUND_COMPLETE)
    public static final String FLOW_OILCARD_RECHARGE_REFUND_COMPLETE_PRINT_RECEIPT = "Consumer.PrintReceipt." + TopicDest.FLOW_OILCARD_RECHARGE_REFUND_COMPLETE;

    // 税筹业务
    @Queue(desc = "税筹提现完成-账务处理", vtopic = TopicDest.TAX_GRANT_COMPLETE)
    public static final String TAX_GRANT_COMPLETE_ACCOUNT = "Consumer.accountProcess." + TopicDest.TAX_GRANT_COMPLETE;
    @Queue(desc = "税筹提现完成-导入批次处理", vtopic = TopicDest.TAX_GRANT_COMPLETE)
    public static final String TAX_GRANT_COMPLETE_IMPORT = "Consumer.importProcess." + TopicDest.TAX_GRANT_COMPLETE;
    @Queue(desc = "(直签)税筹商户提现完成-导入批次处理", vtopic = TopicDest.TAX_DIRECT_SIGN_GRANT_COMPLETE)
    public static final String TAX_DIRECT_SIGN_GRANT_COMPLETE_IMPORT = "Consumer.importProcess." + TopicDest.TAX_DIRECT_SIGN_GRANT_COMPLETE;

    /**
     * 二维map，第一个key为虚拟队列的名称，第二个key为属性值，value为该属性上@Queue注解的描述内容
     *
     * @return
     */
    public static Map<String, Map<String, String>> toVTopicMap() {
        Map<String, Map<String, String>> outMap = new LinkedHashMap<>();

        Field[] fields = VTopicConsume.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null) {
                continue;
            }

            String vtopic = queue.vtopic();
            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(VTopicConsume.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            if (!outMap.containsKey(vtopic)) {
                outMap.put(vtopic, new LinkedHashMap<>());
            }
            outMap.get(vtopic).put(name, desc);
        }
        return outMap;
    }

    public static Map<String, String> getMap(String vtopic) {
        if (vtopic == null || vtopic.trim().length() <= 0 || !vtopic.startsWith("VirtualTopic.")) {
            return new LinkedHashMap<>();
        }

        Map<String, String> map = new LinkedHashMap<>();
        Field[] fields = VTopicConsume.class.getDeclaredFields();
        for (Field field : fields) {
            Queue queue = field.getAnnotation(Queue.class);
            if (queue == null || !vtopic.equals(queue.vtopic())) {
                continue;
            }

            String desc = queue.desc();
            String name;
            try {
                field.setAccessible(true);
                name = String.valueOf(field.get(VTopicConsume.class));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            map.put(name, desc);
        }
        return map;
    }
}
