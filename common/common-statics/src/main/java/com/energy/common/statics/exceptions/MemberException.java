package com.energy.common.statics.exceptions;

/**
 * 会员相关的异常 MemberException
 *
 * <AUTHOR>
 * @date 2019/12/9
 */
public class MemberException extends BizException {
    /***
     * 系统异常
     */
    public static final int SYS_EXCEPTION = 108008001;

    public static final int DATA_EXCEPTION = 108008005;

    public static final MemberException IDENTITY_NOT_EXIST = new MemberException(108008002, "身份审核记录不存在");

    public static final MemberException MEMBER_NOT_EXIST = new MemberException(108008003, "会员信息不存在");

    public static final MemberException DATA_VALIDATION_ERROR = new MemberException(108008004, "数据校验异常");

    public static final MemberException EXCEED_MAX_TEMPLATE = new MemberException(108008006, "模板数量超过限制");

    public static final MemberException MEMBER_PHONENO_EXIST = new MemberException(108008007, "会员手机号已存在");

    public static final MemberException NO_ALLOW_ADD_LEVEL = new MemberException(108008008, "高等级的升级条件必须更高");

    public static final MemberException NO_ALLOW_DELETE_DEFAULT = new MemberException(108008009, "默认等级不能删除");

    public static final MemberException NO_ALLOW_DELETE_EXIST_MEMBER = new MemberException(108008010, "已有会员使用模板不能删除");

    public static final MemberException NO_ALLOW_UPDATE_UPGRADETYPE = new MemberException(108008011, "不能修改模板升级类型");

    public static final MemberException MEMBER_IDENTITY_EXIST = new MemberException(108008012, "已提交过待审核的身份申请");

    public static final MemberException IDENTITY_TEMPLATE_NO_EXIST = new MemberException(108008013, "该商户暂未开通该身份模板");

    public static final MemberException IDENTITY_TEMPLATE_IDENTICAL_EXIST = new MemberException(108008014, "已存在相同类型的模板");

    public static final MemberException ONLY_DELETE_MAX_LEVEL = new MemberException(108008015, "只能删除最高等级");

    public static final MemberException NO_ALLOW_UPDATE_UPGRADE_CONDITION = new MemberException(108008016, "修改后的升级条件不能高于上一等级或低于下一等级");

    public static final MemberException MUST_SET_UP_UPGRADE = new MemberException(108008017, "升级条件不能为空");

    public static final MemberException NO_ALLOW_UPDATE_DEFAULT_UPGRADE = new MemberException(108008018, "默认等级不能修改升级条件");

    public static final MemberException EXIST_IDENTICAL_LEVELNAME = new MemberException(108008019, "已存在相同的等级名称");

    public static final MemberException MEMBER_IDENTITY_SAME = new MemberException(108008020, "已成为该身份类型会员");

    public static final MemberException NO_ALLOW_DELETE_EXIST_APPLY = new MemberException(108008021, "存在未审核的身份申请，请审核后再删除");

    public static final MemberException NO_ALLOW_AUTH_TYPE = new MemberException(108008022, "不支持该身份认证方式");

    /**
     * 权益相关start
     **/
    public static final MemberException PRIORITY_NOT_EXIST = new MemberException(108009001, "权益不存在");
    public static final MemberException NOT_ALLOW_TO_PUTAWAY = new MemberException(108009002, "非待上架与下架状态不可上架");
    public static final MemberException NOT_ALLOW_TO_SOLD_OUT = new MemberException(108009003, "非正常状态不可下架");
    public static final MemberException MCH_NOT_EXIST = new MemberException(108009004, "商户信息不存在");
    public static final MemberException MEMBER_PRIORITY_ERR = new MemberException(108009005, "会员权益不存在");
    public static final MemberException MCH_NOT_SUITABLE = new MemberException(108009006, "当前商户不适用于该权益");
    public static final MemberException PRIORITY_USED = new MemberException(108009007, "权益已被使用");
    public static final MemberException PRIORITY_INVALID = new MemberException(108009008, "权益已失效");
    public static final MemberException PRIORITY_USE_WAY_ERR = new MemberException(108009009, "当前权益只支持手动核销");
    public static final MemberException CONSUME_TOO_LITTLE = new MemberException(108009010, "未达到该权益的消费门槛");
    public static final MemberException PRIORITY_NONSTOCK = new MemberException(108009011, "该权益已被领取完毕");
    public static final MemberException PRIORITY_NOT_SUITABLE = new MemberException(108009012, "当前会员不适用于该权益");
    public static final MemberException MEMBER_MCH_NOT_SUITABLE = new MemberException(108009013, "当前会员所在商户不适用于该权益");
    public static final MemberException MEMBER_PRIORITY_GAIN_LIMITED = new MemberException(108009014, "超过权益领取限制");
    public static final MemberException CREDIT_NOT_ENOUGH = new MemberException(108009015, "会员积分不足以兑换权益");
    public static final MemberException UNKNOW_CREDIT_ALTER_REASON = new MemberException(108009016, "未知的积分变动原因");
    public static final MemberException THRESHOLD_TYPE_NOT_SUPPORT = new MemberException(108009017, "暂不支持核销的满减门槛类型");
    public static final MemberException PRIORITY_TYPE_NOT_SUPPORT = new MemberException(108009018, "暂不支持核销该权益类型");
    public static final MemberException DISCOUNT_TYPE_REQURIED_OIL = new MemberException(108009019, "折扣权益只适用于油品订单");
    public static final MemberException PUTWAY_TIME_SHOULD_LESS_THAN_END_TIME = new MemberException(108009020, "上架时间必须小于权益有效结束时间");
    public static final MemberException GROUP_MCH_WITHOUT_CHILD_MCH = new MemberException(108009021, "该集团商户未配置收款商户");
    public static final MemberException OIL_TYPE_NOT_SUITABLE = new MemberException(108009022, "当前油品不适用该权益");
    public static final MemberException LEVEL_TEMPLATE_NOT_EXIST = new MemberException(108009023, "等级模板不存在");
    public static final MemberException IDENTITY_TEMPLATE_NOT_EXIST = new MemberException(108009024, "身份模板不存在");
    public static final MemberException IDENTITY_PRIORITY_INVALID = new MemberException(108009025, "身份权益不可用");
    public static final MemberException PRIORITY_TOTAL_COUNT_TOO_SMALL = new MemberException(108009026, "权益数量需大于0");
    public static final MemberException PRIORITY_TOTAL_COUNT_SHOULD_GREATER_THAN_LIMIT = new MemberException(108009027, "权益数量需大于等于会员限制数量");
    public static final MemberException NOT_ARRIVE_PIRORITY_USE_DATE = new MemberException(108009028, "未到达权益可用时间");
    public static final MemberException PRIORITY_CONFIG_ERR = new MemberException(108009029, "挂牌单价低于优惠单价，请和收银员确认价格配置是否正确");
    public static final MemberException QR_CODE_ID_REQUIRED = new MemberException(108009030, "权益二维码id不能为空");
    public static final MemberException QR_CODE_INVALID = new MemberException(108009031, "权益二维码已失效");
    public static final MemberException PRIORITY_TYPE_NOT_SUPPORT_TO_GAIN = new MemberException(108009032, "该权益类型不可领取");
    public static final MemberException NOT_ALLOW_TO_USE_PRIORITY_DIRECTLY = new MemberException(108009033, "该权益类型不支持直接核销");
    public static final MemberException NOT_ALLOW_TO_COMBINATE = new MemberException(108009034, "该权益类型不支持组合核销");
    public static final MemberException MEMBER_INFO_ERROR = new MemberException(108009035, "会员信息错误");
    /** 权益相关end**/

    /** 营销活动相关start **/
    public static final MemberException ACTIVITY_TOTAL_COUNT_SHOULD_GREATER_THAN_LIMIT = new MemberException(108011001, "活动发放数量需大于等于会员发放份数限制");
    public static final MemberException START_TIME_SHOULD_LESS_THAN_END_TIME = new MemberException(108011002, "活动开始时间必须小于结束时间");
    public static final MemberException ACTIVITY_NOT_EXIST = new MemberException(108011003, "活动不存在");
    public static final MemberException NOT_ALLOW_TO_PAUSE_ACTIVITY = new MemberException(108011004, "非进行中状态的活动不允许暂停");
    public static final MemberException NOT_ALLOW_TO_START_ACTIVITY = new MemberException(108011004, "非待开始/暂停中状态的活动不允许开始");
    public static final MemberException ACTIVITY_TOTAL_COUNT_TOO_FEW = new MemberException(108011004, "活动发放数量需大于0");
    public static final MemberException THRESHOLD_MIN_SHOULD_LESS_THAN_MAX = new MemberException(108011005, "门槛设置错误");
    public static final MemberException ACTIVITY_INVITER_AWARD_GIVE_COMPLETE = new MemberException(108011006, "活动邀请者奖励已发放完毕");
    public static final MemberException ACTIVITY_INVITEE_AWARD_GIVE_COMPLETE = new MemberException(108011007, "活动被邀请者奖励已发放完毕");
    public static final MemberException ACTIVITY_AWARD_TYPE_ERR = new MemberException(108011018, "活动奖励类型错误");
    public static final MemberException ACTIVITY_COMPLETE_CONDITION_TYPE_ERR = new MemberException(108011009, "活动完成条件类型错误");
    public static final MemberException INVITE_ACTIVITY_TOTAL_COUNT_SHOULD_GREATER_THAN_LIMIT = new MemberException(108011010, "拉新活动邀请人奖励发放总份数需要大于等于邀请人奖励份数限制");
    public static final MemberException INVITE_ACTIVITY_TOTAL_COUNT_IS_EVEN = new MemberException(108011011, "拉新活动发放份数必须为大于0的双数");
    public static final MemberException INVITE_ACTIVITY_NOT_ALLOW_TIME_CROSSING = new MemberException(108011012, "不允许存在时间交叉的拉新活动");
    public static final MemberException INVITE_ACTIVITY_NOT_ALLOW_RESTART = new MemberException(108011012, "不允许重新开启这个活动，该活动跟待开始活动存在时间交叉");
    public static final MemberException INVITE_ACTIVITY_MAX_PRIORITY_COUNT = new MemberException(108011013, "添加的权益数量不能超过5个");
    /** 营销活动相关end **/

    /** 微信会员卡相关start **/
    public static final MemberException WEIXIN_CARD_CREATE_ERROR = new MemberException(108009030, "微信创建会员卡异常");
    public static final MemberException WEIXIN_CARD_FIELD_ERROR = new MemberException(108009031, "微信设置开卡字段异常");
    public static final MemberException WEIXIN_CARD_URL_ERROR = new MemberException(108009032, "微信获取开卡组件链接异常");
    public static final MemberException WEIXIN_CARD_CODE_ENCRYPT_ERROR = new MemberException(108009033, "微信Code解码异常");
    public static final MemberException WEIXIN_CARD_ACTIVATE_ERROR = new MemberException(108009034, "微信激活会员卡异常");
    public static final MemberException WEIXIN_CARD_TOKEN_ERROR = new MemberException(108009035, "微信获取token异常");
    public static final MemberException MCH_MEMBER_CARD_ERROR = new MemberException(108009036, "商户会员卡记录为空");
    public static final MemberException WEIXIN_CARD_GET_USER_INFO_ERROR = new MemberException(108009037, "微信拉取会员填写信息异常");
    /** 微信会员卡相关end **/

    /** 微信模板消息相关start **/
    public static final MemberException WEIXIN_TEMPLATE_IS_EMPTY = new MemberException(108009038, "未配置模板消息");
    public static final MemberException WEIXIN_TEMPLATE_IS_EXISTS_ERROR = new MemberException(108009039, "模板消息不存在");
    public static final MemberException WEIXIN_TEMPLATE_SEND_ERROR = new MemberException(108009040, "发送模板消息异常");
    public static final MemberException WEIXIN_TEMPLATE_LIST_ERROR = new MemberException(108009041, "获取模板列表异常");
    /** 微信模板消息相关end **/

    public MemberException() {
    }

    public MemberException(int sysErrCode, String msg) {
        super(sysErrCode, msg);
    }
}
