package com.energy.common.statics.enums.traffic;

import java.util.Arrays;
import java.util.List;

/**
 * @desc
 * @author: zhouf
 */
public enum CarBelongTypeEnum {

    PRIVATE(1, "私人车"),
    COMPANY(2, "公司车");

    private int code;
    private String desc;

    CarBelongTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CarBelongTypeEnum> toList() {
        return Arrays.asList(CarBelongTypeEnum.values());
    }

    public static CarBelongTypeEnum getEnum(int value) {
        CarBelongTypeEnum resultEnum = null;
        CarBelongTypeEnum[] enumAry = CarBelongTypeEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getCode() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
