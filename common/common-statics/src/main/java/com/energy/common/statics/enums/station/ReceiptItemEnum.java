package com.energy.common.statics.enums.station;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小票打印项枚举
 *
 * <AUTHOR>
 * @date 2020/10/28
 */
public enum ReceiptItemEnum {
    /**
     * 油品
     */
    OilType("油品", 1),

    /**
     * 支付方式
     */
    PayProduct("支付方式", 2),

    /**
     * 油枪
     */
    OilGun("油枪", 3),

    /**
     * 加油卡
     */
    OilCard("加油卡", 4);

    /**
     * 描述
     */
    private String desc;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 构造函数
     */
    ReceiptItemEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ReceiptItemEnum getEnum(int value) {
        ReceiptItemEnum resultEnum = null;
        ReceiptItemEnum[] enumAry = ReceiptItemEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getValue() == value) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

    public static Map<String, Map<String, Object>> toMap() {
        ReceiptItemEnum[] ary = ReceiptItemEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<>();
        for (int num = 0; num < ary.length; num++) {
            Map<String, Object> map = new HashMap<>();
            String key = String.valueOf(getEnum(ary[num].getValue()));
            map.put("value", String.valueOf(ary[num].getValue()));
            map.put("desc", ary[num].getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    public static List toList() {
        ReceiptItemEnum[] ary = ReceiptItemEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("value", String.valueOf(ary[i].getValue()));
            map.put("desc", ary[i].getDesc());
            list.add(map);
        }
        return list;
    }

    public static ReceiptItemEnum getEnumByOilType(String oilType) {
        ReceiptItemEnum resultEnum = null;
        ReceiptItemEnum[] enumAry = ReceiptItemEnum.values();
        for (int i = 0; i < enumAry.length; i++) {
            if (enumAry[i].getDesc().equals(oilType)) {
                resultEnum = enumAry[i];
                break;
            }
        }
        return resultEnum;
    }

}
