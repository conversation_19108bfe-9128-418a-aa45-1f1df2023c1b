package com.energy.common.statics.enums.station;

import java.util.Arrays;
import java.util.List;

/**
 * @desc: 收银员账号类型
 * @author: zjzj1996
 * @date： 2020/09/14 14:24
 * @modified by:
 */
public enum CashierAccountTypeEnum {

    ADMINISTRATOR("管理员", 1),
    OPERATOR("操作员", 2),
    ;

    private String desc;

    private int code;

    CashierAccountTypeEnum (String desc, int code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static List<CashierAccountTypeEnum> toList(){
        return Arrays.asList(CashierAccountTypeEnum.values());
    }
}
