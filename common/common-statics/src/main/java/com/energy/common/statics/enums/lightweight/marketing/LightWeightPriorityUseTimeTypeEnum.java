package com.energy.common.statics.enums.lightweight.marketing;

import java.util.Arrays;
import java.util.List;

/**
 * @description 轻量化领取权益使用时间类型
 * @author: 山
 * @date: 2022/10/09
 */
public enum LightWeightPriorityUseTimeTypeEnum {
    /**
     * 日
     */
    DAY(1, "日"),
    /**
     * 周
     */
    WEEK(2, "周"),
    /**
     * 月
     */
    MONTH(3, "月"),
    /**
     * 年
     */
    YEAR(4, "年"),
    ;


    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    LightWeightPriorityUseTimeTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static LightWeightPriorityUseTimeTypeEnum getEnum(int value) {
        LightWeightPriorityUseTimeTypeEnum resultEnum = null;
        for (LightWeightPriorityUseTimeTypeEnum typeEnum : LightWeightPriorityUseTimeTypeEnum.values()) {
            if (typeEnum.getValue() == value) {
                resultEnum = typeEnum;
                break;
            }
        }
        return resultEnum;
    }

    public static List<LightWeightPriorityUseTimeTypeEnum> toList() {
        return Arrays.asList(LightWeightPriorityUseTimeTypeEnum.values());
    }
}
