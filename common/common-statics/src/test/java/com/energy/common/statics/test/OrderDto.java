package com.energy.common.statics.test;

import com.energy.common.statics.validation.*;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderDto implements Serializable {
    @NotEmpty(message = "订单号不能为空", scenes = {"add","edit"})//在 add、edit 场景下校验
    @NotChinese(message = "订单号不能含有中文字符", scenes = "add")//仅在 add 场景下才校验
    @Length(message = "订单号长度须介于[5,15]之间", min = 5, max = 15, scenes = "add")//仅在 add 场景下才校验
    private String orderNo;

    /**
     * 当一个属性存在多个注解时，会按照从上到下的顺序进行校验
     */
    @Numeric(message = "商品编号须为纯数字", scenes = "add")//仅在 add 场景下才校验
    @NotEmpty(message = "商品编号不能为空", scenes = "add")//仅在 add 场景下才校验
    @Length(message = "商品编号长度固定为16", min = 16, max = 16, scenes = "add")//仅在 add 场景下才校验
    private String productNo;

    @IntRange(message = "商品数量须介于[1,5]之间", min = 1, max = 5, scenes = "add")//仅在 add 场景下才校验
    @NotNull(message = "商品数量不能为空", scenes = "add")//仅在 add 场景下才校验
    private Integer quantity;

    @DecimalRange(message = "商品价格须介于[0,90000.56]之间", min = 0, max = 90000.56, scenes = "add")//仅在 add 场景下才校验
    @NotNull(message = "商品价格不能为空", scenes = "add")//仅在 add 场景下才校验
    private BigDecimal price;

    @Phone(message = "手机号格式错误", scenes = "add")//仅在 add 场景下才校验
    private String mobile;

    @Length(message = "备注长度不能超过32", max = 32)//不填场景，则表示在任意场景下都需要校验
    private String remark;//备注说明

    @NotEmpty(message = "快递单号不能为空", scenes = "edit")//仅在 edit 场景下才校验
    @Length(message = "快递单号长度须在16~32之间", min = 16, max = 32, scenes = "edit")//仅在 edit 场景下才校验
    private String expressNo;//快递单号

    @AllowInt(message = "订单状态值错误", allows = {2,3}, scenes = "edit")
    private Integer status;//订单状态，1=已创建 2=已支付 3=已取消


    /**--------------------------- 以下注解用以测试故意给错的情况  --------------------------- */
//    @Numeric(message = "'注解要求参数类型为String，实际给了Integer' 的情况", scenes = "testWrong")
    private Integer wrong1;//用以测试故意给错注解，经过测试，系统会报异常：java.lang.Integer cannot be cast to java.lang.String
//    @IntRange(message = "'注解要求参数类型为Integer，实际给了String' 的情况", scenes = "testWrong")
    private String wrong2;//用以测试故意给错注解，经过测试，系统会报异常：java.lang.String cannot be cast to java.lang.Integer
    @DecimalRange(message = "'注解要求参数类型为BigDecimal，实际给了String' 的情况", scenes = "testWrong")
    private String wrong3;//用以测试故意给错注解，经过测试，系统会报异常：java.lang.String cannot be cast to java.math.BigDecimal

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWrong1() {
        return wrong1;
    }

    public void setWrong1(Integer wrong1) {
        this.wrong1 = wrong1;
    }

    public String getWrong2() {
        return wrong2;
    }

    public void setWrong2(String wrong2) {
        this.wrong2 = wrong2;
    }

    public String getWrong3() {
        return wrong3;
    }

    public void setWrong3(String wrong3) {
        this.wrong3 = wrong3;
    }
}
