package com.energy.common.statics.test;

import com.energy.common.statics.util.PoJoValidUtil;

import java.math.BigDecimal;

public class ValidateTest {

    public static void main(String[] args) {
        createOrder();
//        updateOrder();
        testWrong();
    }

    /**
     * 模拟订单创建时的校验
     */
    public static void createOrder() {
        OrderDto orderDto = new OrderDto();
        orderDto.setOrderNo("ORD1234456778");
        orderDto.setProductNo("8594030933876546");
        orderDto.setQuantity(5);
        orderDto.setPrice(BigDecimal.valueOf(50.09));
        orderDto.setMobile("13800138000");
        orderDto.setRemark("这是一个备注");

//        PoJoValidUtil.validate(orderDto, "add", BizException.class);
        String errMsg = PoJoValidUtil.validate(orderDto, "add");
        System.out.println("createOrder校验结果为 errMsg: " + errMsg);
    }

    /**
     * 模拟订单更新时的校验
     */
    public static void updateOrder() {
        OrderDto orderDto = new OrderDto();
        orderDto.setOrderNo("ORD1234456778");
        orderDto.setRemark("这是一个备注");
        orderDto.setExpressNo("123456789012345678");
        orderDto.setStatus(6);

//        PoJoValidUtil.validate(orderDto, "edit", BizException.class);
        String errMsg = PoJoValidUtil.validate(orderDto, "edit");
        System.out.println("updateOrder校验结果为 errMsg: " + errMsg);
    }

    public static void testWrong() {
        OrderDto orderDto = new OrderDto();
        orderDto.setRemark("这是一个备注");
//        orderDto.setWrong1(6);
//        orderDto.setWrong2("");
        orderDto.setWrong3(null);

        String errMsg = PoJoValidUtil.validate(orderDto, "testWrong");
        System.out.println("testWrong校验结果为 errMsg: " + errMsg);
    }
}
