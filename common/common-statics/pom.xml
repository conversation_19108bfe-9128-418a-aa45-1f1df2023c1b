<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.energy</groupId>
        <artifactId>common-parent</artifactId>
        <version>1.0-Release</version>
    </parent>

    <artifactId>common-statics</artifactId>
    <description>共用的静态模块，可定义：Enum、Exception、Const、POJO、Annotation 等静态类型的类</description>
</project>